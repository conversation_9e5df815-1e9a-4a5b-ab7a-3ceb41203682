import pandas as pd
import plotly.graph_objects as go
import os
import asyncio
import numpy as np
from pdf_helper import convert_html_to_one_page_pdf


import aiohttp
import re

async def fetch_purchase_experience(access_token: str, item_id: str, locale: str = "pt_BR"):
    headers = {'Authorization': f'Bearer {access_token}'}
    url = f'https://api.mercadolibre.com/reputation/items/{item_id}/purchase_experience/integrators?locale={locale}'

    print(f"Fetching purchase experience data for item {item_id}")

    try:
        async with aiohttp.ClientSession() as session:
            async with session.get(url, headers=headers) as response:
                if response.status == 200:
                    data = await response.json()
                    print(f"Successfully fetched data for item {item_id}")
                    return data
                else:
                    print(f"Error fetching data for item {item_id}, locale {locale}: {response.status}")
                    return None
    except aiohttp.ClientError as e:
        print(f"AIOHTTP error for item {item_id}, locale {locale}: {e}")
        return None

async def fetch_purchase_experience_batch(access_token: str, item_ids: list, locale: str = "pt_BR"):
    """Fetch purchase experience data for multiple items"""
    results = {}
    for item_id in item_ids:
        data = await fetch_purchase_experience(access_token, item_id, locale)
        if data:
            results[item_id] = data
        # Add a small delay to avoid rate limiting
        await asyncio.sleep(0.1)
    return results

def process_purchase_experience_data(data):
    """Process purchase experience data into a readable format"""
    if not data or not isinstance(data, dict):
        print("Purchase experience data is empty or not a dictionary")
        return None

    # Get overall rating from reputation field
    reputation_data = data.get('reputation', {})

    # Format the rating value
    rating_value = reputation_data.get('value')
    rating_text = reputation_data.get('text', '')
    rating_color = reputation_data.get('color', '')

    # Check for the special case where there's not enough data (-1 value and gray color)
    if rating_color.lower() == 'gray' and rating_value == -1:
        # Check if there's an empty_state_title in metrics_details
        if 'metrics_details' in data and 'empty_state_title' in data['metrics_details']:
            formatted_rating = data['metrics_details']['empty_state_title']
        else:
            formatted_rating = "Sem dados suficientes"
            # Check if there's a title indicating not enough data
            if 'title' in data and data['title'].get('text', '').startswith('Ainda não é possível medir'):
                formatted_rating = "Ainda não é possível medir"
    elif rating_value is None:
        formatted_rating = "Sem dados"
    elif isinstance(rating_value, (int, float)):
        if rating_value == -1:  # Another way to check for insufficient data
            # Check if there's an empty_state_title in metrics_details
            if 'metrics_details' in data and 'empty_state_title' in data['metrics_details']:
                formatted_rating = data['metrics_details']['empty_state_title']
            else:
                formatted_rating = "Sem dados suficientes"
        else:
            formatted_rating = f"{rating_value}"
            if rating_text:
                formatted_rating = f"{rating_value} - {rating_text}"
    else:
        formatted_rating = str(rating_value)

    # Map color to status
    if rating_color.lower() == 'green':
        status = 'Excelente'
    elif rating_color.lower() == 'yellow':
        status = 'Atenção'
    elif rating_color.lower() == 'red':
        status = 'Urgente'
    elif rating_color.lower() == 'gray':
        status = 'Neutro'
    else:
        status = 'Info'

    # Initialize result structure
    result = {
        'overall_rating': formatted_rating,
        'overall_status': status,
        'metrics': [],
        'problems': []
    }

    # Process problems from metrics_details
    metrics_details = data.get('metrics_details', {})

    # Check for empty state message
    empty_state_title = metrics_details.get('empty_state_title', '')
    if empty_state_title and 'Você não teve vendas com problemas' in empty_state_title:
        # Add a positive metric for products without sales problems
        result['metrics'].append({
            'id': 'no_problems',
            'title': 'Sem Problemas nas Vendas',
            'value': 'Excelente',
            'status': 'success',
            'description': empty_state_title
        })

        # For grey items with no problems, we want to show the empty_state_title message
        # but still mark them as insufficient_data so they get special handling in the UI
        if rating_color.lower() == 'gray' and rating_value == -1:
            formatted_rating = empty_state_title

    problems_list = metrics_details.get('problems', [])

    for problem in problems_list:
        # Extract problem details
        problem_key = problem.get('key', 'UNKNOWN')
        problem_color = problem.get('color', '#cccccc')
        problem_quantity = problem.get('quantity', 'N/A')
        problem_tag = problem.get('tag', '')

        # Get level two details (problem category)
        level_two = problem.get('level_two', {})
        level_two_key = level_two.get('key', '')
        level_two_title = level_two.get('title', {}).get('text', 'Problema não especificado')

        # Get level three details (specific problem and remedy)
        level_three = problem.get('level_three', {})
        level_three_key = level_three.get('key', '')
        level_three_title = level_three.get('title', {}).get('text', '')
        level_three_remedy = level_three.get('remedy', {}).get('text', '')

        # Create a status based on problem color
        if problem_color.lower() in ['#7267e4', '#4a90e2', '#3498db', '#2980b9']:
            status = 'info'  # Blue-ish colors
        elif problem_color.lower() in ['#e74c3c', '#c0392b', '#ff0000', '#cc0000']:
            status = 'danger'  # Red-ish colors
        elif problem_color.lower() in ['#f39c12', '#e67e22', '#d35400', '#ff9800']:
            status = 'warning'  # Orange/yellow-ish colors
        else:
            status = 'secondary'  # Default for other colors

        # Format problem data
        problem_data = {
            'key': problem_key,
            'color': problem_color,
            'quantity': problem_quantity,
            'tag': problem_tag,
            'status': status,
            'level_two_key': level_two_key,
            'level_two_title': level_two_title,
            'level_three_key': level_three_key,
            'level_three_title': level_three_title,
            'remedy': level_three_remedy
        }

        result['problems'].append(problem_data)

    # Process distribution data for additional metrics
    if 'metrics_details' in data and 'distribution' in metrics_details:
        distribution = metrics_details.get('distribution', {})
        level_one_items = distribution.get('level_one', [])

        for item in level_one_items:
            metric_data = {
                'id': item.get('key', 'unknown'),
                'title': item.get('title', {}).get('text', 'Métrica'),
                'value': f"{item.get('percentage', 0):.1f}%",
                'status': 'info' if item.get('color', '').lower() in ['#7267e4', '#4a90e2'] else 'warning',
                'description': ''
            }
            result['metrics'].append(metric_data)

    # Add status information if available
    if 'status' in data:
        status_data = data.get('status', {})
        status_text = status_data.get('text', '')
        if status_text:
            metric_data = {
                'id': 'status',
                'title': 'Status do Anúncio',
                'value': status_text,
                'status': 'warning' if status_data.get('id') == 'paused' else 'info',
                'description': ''
            }
            result['metrics'].append(metric_data)

    return result

def create_purchase_experience_html(purchase_data, df_active, locale="pt_BR"):
    """Create HTML to display purchase experience data"""
    print(f"Creating purchase experience HTML with {len(purchase_data)} products")

    if not purchase_data:
        print("Purchase data is empty, returning empty HTML")
        return ""

    # Import the translation module
    from translations import get_translation

    # Use default locale if specified locale not available
    if locale not in ["pt_BR", "es_AR"]:
        # Handle simplified locale codes
        if locale == "pt":
            locale = "pt_BR"
        elif locale == "es":
            locale = "es_AR"
        else:
            locale = "pt_BR"

    # Create a shorthand function for getting translations with the current locale
    def t(key, **kwargs):
        return get_translation(key, locale, **kwargs)

    html = f"""
    <div class="chart-container purchase-experience-container">
        <h2>{t("purchase_experience_problems")}</h2>
        <div class="info-box">
            <div class="info-icon">⚠️</div>
            <div class="info-content">
                <strong>{t("attention")}:</strong> {t("purchase_experience_info")}
            </div>
        </div>

        <div class="product-cards">
    """

    for item_id, data in purchase_data.items():
        print(f"Processing HTML for product {item_id}")
        if not data:
            print(f"No data for product {item_id}, skipping")
            continue

        # Check if this is a product with insufficient data but we still want to show it
        insufficient_data = False
        if data.get('overall_rating') == t("no_sales_problems"):
            insufficient_data = True

        # Skip products that don't have problems and don't have insufficient data
        if not data.get('problems') and not insufficient_data:
            print(f"No problems found for product {item_id}, skipping")
            continue

        print(f"Data for product {item_id}: overall_rating={data.get('overall_rating')}, problems={len(data.get('problems', []))}")

        product_name = item_id  # Use only the numeric ID

        # Determine status class based on status
        status = data['overall_status']
        status_class = "status-success" if status == 'success' else "status-warning" if status == 'warning' else "status-danger"

        # We're not showing metrics anymore as requested

        # Format problems if available
        problems_html = ""

        # Check if this is a product with insufficient data
        if insufficient_data:
            # Check if this is a grey item with empty_state_title
            empty_state_message = ""
            if 'metrics_details' in data and 'empty_state_title' in data['metrics_details']:
                empty_state_message = data['metrics_details']['empty_state_title']

            if empty_state_message and "não teve vendas com problemas" in empty_state_message:
                # Display only the empty_state_title message for grey items with no problems
                problems_html = f"""
                <div class="product-problems">
                    <div class="insufficient-data-message" style="background-color: #e8f5e9; border-left: 3px solid #28a745;">
                        <span class="info-icon">✅</span>
                        <div class="info-content">
                            <p style="font-weight: 500; color: #28a745;">{empty_state_message}</p>
                        </div>
                    </div>
                </div>
                """

        elif 'problems' in data and data['problems']:
            problems_html = f"""
            <div class="product-problems">
                <h4>{t("main_problems")}</h4>
                <div class="problems-list">
            """

            for problem in data['problems'][:2]:  # Show only top 2 problems to save space
                problem_title = problem.get('level_two_title', t("unspecified_problem"))
                problem_detail = problem.get('level_three_title', '')
                problem_remedy = problem.get('remedy', '')
                problem_quantity = problem.get('quantity', 'N/A')
                problem_status = problem.get('status', 'secondary')
                problem_tag = problem.get('tag', '')

                problems_html += f"""
                <div class="problem-item status-{problem_status}">
                    <div class="problem-header">
                        <span class="problem-tag">{problem_tag}</span>
                        <span class="problem-quantity">{problem_quantity}</span>
                    </div>
                    <div class="problem-title">{problem_title}</div>
                """

                if problem_detail:
                    problems_html += f"""<div class="problem-detail">{problem_detail}</div>"""

                if problem_remedy:
                    problems_html += f"""
                    <div class="problem-remedy">
                        <strong>{t("recommendation")}:</strong> {problem_remedy}
                    </div>
                    """

                problems_html += """
                </div>
                """

            problems_html += """
                </div>
            </div>
            """

        html += f"""
        <div class="product-card {status_class}">
            <div class="product-header">
                <h3 class="product-name">{product_name}</h3>
                <span class="status-badge status-{status}">{status.upper()}</span>
            </div>
            <div class="product-rating">
                <div class="rating-label">{t("overall_rating")}</div>
                <div class="rating-value">{data['overall_rating']}</div>
            </div>
            {problems_html}
        </div>
        """

    # Check if any products were added to the HTML
    if "<div class=\"product-card" not in html:
        print("No products with problems found, returning empty HTML")
        return ""

    html += f"""
        </div>

        <div class="tip-box">
            <div class="tip-icon">💡</div>
            <div class="tip-content">
                <strong>{t("tip")}:</strong> {t("purchase_experience_tip")}
            </div>
        </div>
    </div>

    <style>
        /* Purchase Experience Styles */"""
    html +=""".purchase-experience-container {
            background: linear-gradient(to bottom, #ffffff, #f9fbff);
            border-radius: 12px;
            box-shadow: 0 8px 30px rgba(0, 0, 0, 0.08);
            border: none;
            overflow: hidden;
            margin-bottom: 30px;
            padding: 25px;
        }

        .purchase-experience-container h2 {
            color: #2c3e50;
            font-size: 1.5em;
            margin-bottom: 20px;
            border-bottom: 2px solid #e7eef7;
            padding-bottom: 12px;
        }

        .info-box, .tip-box {
            display: flex;
            align-items: flex-start;
            margin-bottom: 25px;
            padding: 15px;
            border-radius: 8px;
            background-color: #f1f8ff;
            border-left: 4px solid #4a90e2;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
        }

        .tip-box {
            margin-top: 25px;
            background-color: #fffbf1;
            border-left: 4px solid #f0ad4e;
        }

        .info-box:hover, .tip-box:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
        }

        .info-icon, .tip-icon {
            font-size: 1.5em;
            margin-right: 15px;
            line-height: 1;
        }

        .info-content, .tip-content {
            flex: 1;
            font-size: 0.95em;
            line-height: 1.5;
        }

        .product-cards {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
            margin: 25px 0;
        }

        .product-card {
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
            padding: 20px;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            border-top: 4px solid #e0e0e0;
        }

        .product-card.status-success {
            border-top-color: #28a745;
        }

        .product-card.status-warning {
            border-top-color: #ffc107;
        }

        .product-card.status-danger {
            border-top-color: #dc3545;
        }

        .product-card.status-secondary {
            border-top-color: #6c757d;
        }

        .product-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        }

        .product-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 1px solid #f0f0f0;
        }

        .product-name {
            font-size: 1em;
            font-weight: 600;
            color: #2c3e50;
            margin: 0;
            overflow: hidden;
            text-overflow: ellipsis;
            max-width: 80%;
            line-height: 1.3;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            white-space: normal;
        }

        .status-badge {
            padding: 4px 8px;
            border-radius: 20px;
            font-size: 0.7em;
            font-weight: 700;
            text-transform: uppercase;
            color: white;
        }

        .status-badge.status-success {
            background-color: #28a745;
        }

        .status-badge.status-warning {
            background-color: #ffc107;
            color: #212529;
        }

        .status-badge.status-danger {
            background-color: #dc3545;
        }

        .status-badge.status-secondary {
            background-color: #6c757d;
        }

        .product-rating {
            display: flex;
            flex-direction: column;
            align-items: center;
            margin-bottom: 15px;
            padding: 10px;
            background-color: #f8f9fa;
            border-radius: 8px;
        }

        .rating-label {
            font-size: 0.8em;
            color: #6c757d;
            margin-bottom: 5px;
        }

        .rating-value {
            font-size: 1.8em;
            font-weight: 700;
            color: #2c3e50;
        }

        .product-metrics h4 {
            font-size: 0.9em;
            color: #6c757d;
            margin-bottom: 10px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .metrics-list {
            list-style-type: none;
            padding: 0;
            margin: 0;
        }

        .metrics-list li {
            display: flex;
            justify-content: space-between;
            padding: 8px 0;
            border-bottom: 1px dashed #e0e0e0;
            font-size: 0.9em;
            position: relative;
            padding-left: 15px;
        }

        .metrics-list li:last-child {
            border-bottom: none;
        }

        .metrics-list li:before {
            content: "•";
            position: absolute;
            left: 0;
            top: 8px;
        }

        .metrics-list li.metric-success:before {
            color: #28a745;
        }

        .metrics-list li.metric-warning:before {
            color: #ffc107;
        }

        .metrics-list li.metric-danger:before {
            color: #dc3545;
        }

        .metric-value {
            font-weight: 600;
            color: #2c3e50;
        }

        /* Problem styles */
        .product-problems {
            margin-top: 20px;
            border-top: 1px solid #f0f0f0;
            padding-top: 15px;
        }

        .product-problems h4 {
            font-size: 0.9em;
            color: #6c757d;
            margin-bottom: 10px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .problems-list {
            display: flex;
            flex-direction: column;
            gap: 12px;
        }

        .problem-item {
            background-color: #f8f9fa;
            border-radius: 8px;
            padding: 12px;
            border-left: 3px solid #6c757d;
        }

        .problem-item.status-info {
            border-left-color: #17a2b8;
        }

        .problem-item.status-warning {
            border-left-color: #ffc107;
        }

        .problem-item.status-danger {
            border-left-color: #dc3545;
        }

        .problem-header {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
        }

        .problem-tag {
            font-size: 0.7em;
            font-weight: 700;
            text-transform: uppercase;
            color: #6c757d;
        }

        .problem-quantity {
            font-size: 0.8em;
            font-weight: 600;
            color: #dc3545;
        }

        .problem-title {
            font-weight: 600;
            font-size: 0.9em;
            color: #2c3e50;
            margin-bottom: 5px;
        }

        .problem-detail {
            font-size: 0.85em;
            color: #6c757d;
            margin-bottom: 8px;
        }

        .problem-remedy {
            font-size: 0.85em;
            color: #2c3e50;
            background-color: #e8f4f8;
            padding: 8px;
            border-radius: 4px;
            margin-top: 8px;
        }

        /* Insufficient data message styling */
        .insufficient-data-message {
            display: flex;
            background-color: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
            border-left: 3px solid #6c757d;
            margin-top: 10px;
        }

        .insufficient-data-message .info-icon {
            font-size: 1.5em;
            margin-right: 15px;
            line-height: 1;
        }

        .insufficient-data-message .info-content {
            flex: 1;
        }

        .insufficient-data-message p {
            margin: 0 0 10px 0;
            font-size: 0.9em;
            color: #495057;
        }

        .insufficient-data-message ul {
            margin: 0;
            padding-left: 20px;
            font-size: 0.85em;
            color: #6c757d;
        }

        .insufficient-data-message li {
            margin-bottom: 5px;
        }
    </style>
    """ # type: ignore

    return html






async def generate_complete_perfromance_report(campaign_data, campaign_products, campagin_id, uid, access_token=None, locale="pt_BR"):
    # Import the translation module
    from translations import get_translation

    # Use default locale if specified locale not available
    if locale not in ["pt_BR", "es_AR"]:
        # Handle simplified locale codes
        if locale == "pt":
            locale = "pt_BR"
        elif locale == "es":
            locale = "es_AR"
        else:
            locale = "pt_BR"

    # Create a shorthand function for getting translations with the current locale
    def t(key, **kwargs):
        return get_translation(key, locale, **kwargs)

    # Extract campaign general information
    campaign_title = campaign_data.get('name', 'N/A')
    campaign_strategy = campaign_data.get('strategy', 'N/A')

    # Get raw dates
    campaign_creation_date_raw = campaign_data.get('date_created', 'N/A')
    campaign_last_updated_raw = campaign_data.get('last_updated', 'N/A')

    # Format dates to be more human-readable (DD/MM/YYYY HH:MM)
    try:
        campaign_creation_datetime = pd.to_datetime(campaign_creation_date_raw)
        campaign_creation_date = campaign_creation_datetime.strftime("%d/%m/%Y %H:%M")
    except (ValueError, TypeError) as e:
        print(f"Error formatting campaign creation date: {e}")
        campaign_creation_date = campaign_creation_date_raw
        # Still try to create the datetime object for later use
        try:
            campaign_creation_datetime = pd.to_datetime(campaign_creation_date_raw)
        except (ValueError, TypeError):
            # If all else fails, use current time as fallback
            campaign_creation_datetime = pd.Timestamp.now()

    try:
        campaign_last_updated = pd.to_datetime(campaign_last_updated_raw).strftime("%d/%m/%Y %H:%M")
    except (ValueError, TypeError) as e:
        print(f"Error formatting campaign last updated date: {e}")
        campaign_last_updated = campaign_last_updated_raw

    campaign_budget = campaign_data.get('budget', 0)


    df = pd.json_normalize(campaign_products)

    # Convert date to datetime
    df['date'] = pd.to_datetime(df['date'])

    # Always use product IDs for display in charts
    # Format product IDs consistently as "Produto {numeric_id}" or "Producto {numeric_id}" based on locale
    product_text = "Produto" if locale == "pt_BR" else "Producto"
    df['product_name'] = df['item_id'].astype(str).apply(lambda x: f"{product_text} {x.replace('MLB', '')}")

    # Calculate ACOS (Advertising Cost of Sale) for each product
    # ACOS = Cost / Revenue
    # Handle division by zero or NaN values
    df['metrics.acos'] = np.where(
        (df['metrics.total_amount'] > 0) & (df['metrics.cost'] > 0),
        (df['metrics.cost'] / df['metrics.total_amount']) * 100,
        0
    )

    # Set ACOS benchmark based on campaign data if available, otherwise use default
    acos_benchmark = campaign_data['metrics'].get('acos_benchmark', 25.0)
    acos_target = campaign_data['metrics'].get('acos_target', 25.0)
    acos_top_search_target = campaign_data['metrics'].get('acos_top_search_target', None)

    # Get unique products that have at least some activity
    active_products = df[df['metrics.prints'] > 0]['item_id'].unique()
    df_active = df[df['item_id'].isin(active_products)]

    # Fill NaN values in metrics to avoid calculation issues
    df_active['metrics.clicks'] = df_active['metrics.clicks'].fillna(0)
    df_active['metrics.prints'] = df_active['metrics.prints'].fillna(0)
    df_active['metrics.cost'] = df_active['metrics.cost'].fillna(0)
    df_active['metrics.total_amount'] = df_active['metrics.total_amount'].fillna(0)

    # Calculate CTR and CPC directly to ensure consistency
    # CTR = Clicks / Impressions
    df_active['metrics.ctr'] = np.where(
        (df_active['metrics.prints'] > 0) & (df_active['metrics.clicks'] > 0),
        (df_active['metrics.clicks'] / df_active['metrics.prints']) * 100,
        0
    )

    # CPC = Cost / Clicks
    df_active['metrics.cpc'] = np.where(
        (df_active['metrics.clicks'] > 0) & (df_active['metrics.cost'] > 0),
        df_active['metrics.cost'] / df_active['metrics.clicks'],
        0
    )

    # Generate summary metrics
    total_clicks = df_active['metrics.clicks'].sum()
    total_impressions = df_active['metrics.prints'].sum()
    total_cost = df_active['metrics.cost'].sum()
    total_revenue = df_active['metrics.total_amount'].sum()
    avg_ctr = (total_clicks / total_impressions) * 100 if total_impressions > 0 else 0
    avg_roas = total_revenue / total_cost if total_cost > 0 else 0
    avg_cvr = campaign_data['metrics'].get('cvr', 0) #* 100  # Conversion rate

    # Calculate overall ACOS
    overall_acos = (total_cost / total_revenue) * 100 if total_revenue > 0 else 0

    # Determine if ACOS is within benchmark range
    acos_status = ""
    if acos_benchmark is not None and acos_benchmark > 0:
        if overall_acos <= acos_benchmark:
            acos_status = t("acos_status_below")
        elif overall_acos <= acos_benchmark * 1.2:
            acos_status = t("acos_status_near")
        else:
            acos_status = t("acos_status_above")
    else:
        # If there's no benchmark, just show the current ACOS without comparison
        acos_status = f"{t('acos_current')}: {overall_acos:.2f}%"

    # Calculate the difference between ACOS target and benchmark (if benchmark exists)
    acos_target_recommendation = ""
    if acos_benchmark is not None and acos_benchmark > 0:
        acos_target_benchmark_diff = abs(acos_target - acos_benchmark) / acos_benchmark * 100
        if acos_target_benchmark_diff > 15:
            if acos_target > acos_benchmark:
                acos_target_recommendation = t("acos_target_adjust_above",
                    acos_target=acos_target,
                    acos_target_benchmark_diff=acos_target_benchmark_diff,
                    acos_benchmark=acos_benchmark
                )
            else:
                acos_target_recommendation = t("acos_target_adjust_below",
                    acos_target=acos_target,
                    acos_target_benchmark_diff=acos_target_benchmark_diff,
                    acos_benchmark=acos_benchmark
                )

    # Get the actual lost impression share data from campaign_data
    lost_by_budget = campaign_data['metrics']['lost_impression_share_by_budget'] * 100
    lost_by_rank = campaign_data['metrics']['lost_impression_share_by_ad_rank'] * 100
    total_lost = lost_by_budget + lost_by_rank
    impression_share = campaign_data['metrics']['impression_share'] * 100 if 'impression_share' in campaign_data['metrics'] else 0
    top_impression_share = campaign_data['metrics'].get('top_impression_share', 0)

    # Calculate budget and rank percentages of total lost impressions
    budget_percentage = (lost_by_budget / total_lost) * 100 if total_lost > 0 else 0
    rank_percentage = (lost_by_rank / total_lost) * 100 if total_lost > 0 else 0

    # Get the sales metrics from campaign_data with safe fallbacks for NaN values
    organic_items_quantity = campaign_data['metrics'].get('organic_items_quantity', 0) or 0
    organic_units_quantity = campaign_data['metrics'].get('organic_units_quantity', 0) or 0
    organic_units_amount = campaign_data['metrics'].get('organic_units_amount', 0) or 0

    advertising_items_quantity = campaign_data['metrics'].get('advertising_items_quantity', 0) or 0

    direct_items_quantity = campaign_data['metrics'].get('direct_items_quantity', 0) or 0
    direct_units_quantity = campaign_data['metrics'].get('direct_units_quantity', 0) or 0
    direct_amount = campaign_data['metrics'].get('direct_amount', 0) or 0

    indirect_items_quantity = campaign_data['metrics'].get('indirect_items_quantity', 0) or 0
    indirect_units_quantity = campaign_data['metrics'].get('indirect_units_quantity', 0) or 0
    indirect_amount = campaign_data['metrics'].get('indirect_amount', 0) or 0

    # Get total units quantity
    units_quantity = campaign_data['metrics'].get('units_quantity', direct_units_quantity + indirect_units_quantity + organic_units_quantity)

    # Calculate SOV (Share of Voice)
    sov = campaign_data['metrics'].get('sov', 0) * 100



    # Create a simplified sales table that only shows title, MLB ID, total sales and units sold
    # Use the existing DataFrame to extract sales data and handle duplicate item_ids

    # Create a copy of the DataFrame for sales processing
    sales_df = df.copy()

    # Fill NaN values in metrics columns
    sales_df['metrics.total_amount'] = sales_df['metrics.total_amount'].fillna(0)
    sales_df['metrics.units_quantity'] = sales_df['metrics.units_quantity'].fillna(0)

    # Handle duplicate item_ids by grouping and aggregating
    # Group by item_id and sum the sales metrics, keeping the first product_name
    sales_grouped = sales_df.groupby('item_id').agg({
        'metrics.total_amount': 'sum',
        'metrics.units_quantity': 'sum',
        'product_name': 'first',  # Use the existing product_name from DataFrame
        'title': 'first',  # Keep title as fallback
    }).reset_index()

    # Filter to only include products with sales > 0
    sales_with_data = sales_grouped[
        (sales_grouped['metrics.total_amount'] > 0) |
        (sales_grouped['metrics.units_quantity'] > 0)
    ].copy()

    # Prepare final product data
    if not sales_with_data.empty:
        # Use the product_name from DataFrame if available, otherwise create fallback names
        def get_product_title(row):
            # First try the existing product_name column (already formatted as "Produto {id}")
            if pd.notna(row['product_name']) and row['product_name']:
                return row['product_name']
            # Fallback to title if available
            elif pd.notna(row['title']) and row['title']:
                return row['title']
            # Fallback to user_product_name if available
            elif pd.notna(row['user_product_name']) and row['user_product_name']:
                return row['user_product_name'].split(' - ')[0]
            else:
                # Create generic name with clean ID
                clean_id = row['item_id'].replace('MLB', '') if isinstance(row['item_id'], str) else row['item_id']
                product_text = "Produto" if locale == "pt_BR" else "Producto"
                return f"{product_text} {clean_id}"

        sales_with_data['final_product_name'] = sales_with_data.apply(get_product_title, axis=1)

        # Extract the data for HTML generation
        product_ids = sales_with_data['item_id'].tolist()
        product_names = sales_with_data['title'].tolist()
        total_sales = sales_with_data['metrics.total_amount'].tolist()
        total_units = sales_with_data['metrics.units_quantity'].tolist()
    else:
        product_ids = []
        product_names = []
        total_sales = []
        total_units = []

    # Create HTML table for sales
    if product_ids:
        # Extract the first three letters from the first product ID for dynamic header
        dynamic_header = "MLB"  # Default fallback
        try:
            if product_ids and len(product_ids) > 0:
                first_product_id = str(product_ids[0])
                # Extract first three letters (or characters) from the product ID
                dynamic_header = first_product_id[:3].upper()
        except Exception as e:
            print(f"Error extracting dynamic header from product ID: {e}")
            # Keep default "MLB" if there's any error
            dynamic_header = "MLB"

        before_after_sales_html = f"""
        <div class='lost-impression-table'>
            <div style="margin-bottom: 15px; background-color: #f1f8ff; padding: 10px; border-radius: 5px;">
                <strong>{t("info")}:</strong> {t("sales_info")}
            </div>
            <table style='width: 100%; border-collapse: collapse; margin-top: 20px;'>
                <thead>
                    <tr style='background-color: #f2f2f2;'>
                        <th style='padding: 12px; text-align: left; border-bottom: 2px solid #ddd;'>{t("product")}</th>
                        <th style='padding: 12px; text-align: center; border-bottom: 2px solid #ddd;'>{dynamic_header}</th>
                        <th style='padding: 12px; text-align: center; border-bottom: 2px solid #ddd;'>{t("units_sold")}</th>
                        <th style='padding: 12px; text-align: center; border-bottom: 2px solid #ddd;'>{t("total_sales")}</th>
                    </tr>
                </thead>
                <tbody>
        """

        # Add rows for each product
        for i, product_title in enumerate(product_names):
            # Format MLB ID for display (remove MLB prefix)
            mlb_id_display = product_ids[i].replace('MLB', '') if isinstance(product_ids[i], str) and product_ids[i].startswith('MLB') else product_ids[i]

            # Format sales amount with comma as decimal separator (Brazilian format)
            sales_formatted = f"{t('currency_symbol')} {total_sales[i]:,.2f}".replace(',', 'X').replace('.', ',').replace('X', '.')

            # Alternate row colors for better readability
            row_color = "#f8f9fa" if i % 2 == 0 else "#ffffff"

            before_after_sales_html += f"""
            <tr style='background-color: {row_color};'>
                <td style='padding: 10px; text-align: left; border-bottom: 1px solid #ddd;'>{product_title}</td>
                <td style='padding: 10px; text-align: center; border-bottom: 1px solid #ddd;'>{mlb_id_display}</td>
                <td style='padding: 10px; text-align: center; border-bottom: 1px solid #ddd;'>{int(total_units[i])}</td>
                <td style='padding: 10px; text-align: center; border-bottom: 1px solid #ddd;'>{sales_formatted}</td>
            </tr>
            """

        before_after_sales_html += """
                </tbody>
            </table>
        </div>
        """

        # Add a summary section
        total_sales_sum = sum(total_sales)
        total_units_sum = sum(total_units)

        # Format total sales with comma as decimal separator (Brazilian format)
        total_sales_formatted = f"{t('currency_symbol')} {total_sales_sum:,.2f}".replace(',', 'X').replace('.', ',').replace('X', '.')

        before_after_sales_html += f"""
        <div style='margin-top: 15px; padding: 15px; background-color: #f8f9fa; border-radius: 5px; border-left: 4px solid #4a90e2'>
            <strong>{t("summary")}:</strong> {t("sales_summary",
                total_units=int(total_units_sum),
                total_sales=total_sales_formatted
            )}
        </div>
        """
    else:
        before_after_sales_html = f"<p>{t('no_products_with_sales')}</p>"

    # Comment out the original code that used sales_history
    """
    # Process sales_before_ads data to compare sales before and after campaign creation
    # Initialize storage for processed data
    before_after_sales_data = {}

    # Process the sales before ads data
    for product_id, dates_data in sales_history.items():
        if product_id not in before_after_sales_data:
            before_after_sales_data[product_id] = {'before': 0, 'after': 0, 'name': ''}

        # Get product name if it exists in the active products
        product_name = ''
        for product in campaign_products:
            if product.get('item_id') == product_id:
                # Try to get product name from different possible fields
                if 'user_product_name' in product and product.get('user_product_name'):
                    product_name = product.get('user_product_name', '').split(' - ')[0]
                elif 'product_name' in product and product.get('product_name'):
                    product_name = product.get('product_name', '')
                elif 'title' in product and product.get('title'):
                    product_name = product.get('title', '')
                else:
                    # Remove MLB prefix from product ID for cleaner display
                    clean_id = product_id.replace('MLB', '') if isinstance(product_id, str) else product_id
                    product_name = f"Produto {clean_id}"

                before_after_sales_data[product_id]['name'] = product_name
                break

        # Count sales before and after campaign creation
        try:
            # Ensure campaign_creation_datetime is timezone-naive for comparison
            campaign_date_naive = campaign_creation_datetime
            if hasattr(campaign_date_naive, 'tzinfo') and campaign_date_naive.tzinfo is not None:
                campaign_date_naive = campaign_date_naive.tz_localize(None)

            # Calculate the start date for the "before" period (same number of days as the "after" period)
            now = pd.Timestamp.now()
            if hasattr(now, 'tzinfo') and now.tzinfo is not None:
                now = now.tz_localize(None)
            days_since_campaign = (now - campaign_date_naive).days
            before_start_date = campaign_date_naive - pd.Timedelta(days=days_since_campaign)

            # Convert to numpy datetime64 for consistent comparison
            campaign_date_np = np.datetime64(campaign_date_naive)
            before_start_date_np = np.datetime64(before_start_date)

            for date_str, count in dates_data.items():
                try:
                    # Convert to datetime and handle potential parsing errors
                    sale_date = pd.to_datetime(date_str, errors='coerce')

                    # Skip invalid dates
                    if pd.isna(sale_date):
                        continue

                    # Ensure both dates are timezone-naive for comparison
                    if hasattr(sale_date, 'tzinfo') and sale_date.tzinfo is not None:
                        sale_date = sale_date.tz_localize(None)

                    # Convert to numpy datetime64 for consistent comparison
                    sale_date_np = np.datetime64(sale_date)

                    # Only count "before" sales if they're within the equivalent time period
                    if before_start_date_np <= sale_date_np < campaign_date_np:
                        before_after_sales_data[product_id]['before'] += count
                    elif sale_date_np >= campaign_date_np:
                        before_after_sales_data[product_id]['after'] += count
                except Exception as e:
                    print(f"Error processing sale date {date_str} for product {product_id}: {e}")
                    # Skip this date if there's an error
                    continue
        except Exception as e:
            print(f"Error processing sales history for product {product_id}: {e}")
            # If there's an error with the entire product, just keep the counts at 0
    """

    # Create separate charts for ads sales and organic sales
    fig_ads_sales = go.Figure()
    fig_organic_sales = go.Figure()

    # Define a consistent color palette for better visualization
    color_palette = [
        '#4285F4', '#EA4335', '#FBBC05', '#34A853',  # Google colors
        '#3b5998', '#8a3ab9', '#e4405f', '#0077B5',  # Social media colors
        '#00b8d4', '#ff6d00', '#aa00ff', '#64dd17',  # Material design colors
        '#1e88e5', '#d81b60', '#ffc107', '#00897b',  # More material design
        '#5e35b1', '#43a047', '#fb8c00', '#546e7a'   # Even more colors
    ]

    for idx, product in enumerate(active_products):
        product_data = df_active[df_active['item_id'] == product].copy()

        # Skip if product data is completely empty
        if product_data.empty:
            continue

        # Always use product ID for display
        try:
            # Clean up MLB ID for display
            clean_id = str(product).replace('MLB', '') if isinstance(product, str) else product
        except Exception as e:
            print(f"Error getting product name for {product}: {e}")
            # Clean up MLB ID for display
            clean_id = str(product).replace('MLB', '') if isinstance(product, str) else product

        # Make a copy to avoid SettingWithCopyWarning
        product_data_clean = product_data.copy()

        # Check if metrics columns exist, if not try to create them from nested metrics
        if 'metrics.total_amount' not in product_data_clean.columns and 'metrics' in product_data_clean.columns:
            # Try to extract from nested metrics dictionary
            product_data_clean['metrics.total_amount'] = product_data_clean['metrics'].apply(
                lambda x: x.get('total_amount', 0) if isinstance(x, dict) else 0
            )
            product_data_clean['metrics.organic_units_quantity'] = product_data_clean['metrics'].apply(
                lambda x: x.get('organic_units_quantity', 0) if isinstance(x, dict) else 0
            )

        # Fill NaN values with 0 for better visualization
        product_data_clean['metrics.total_amount'] = product_data_clean['metrics.total_amount'].fillna(0)
        product_data_clean['metrics.organic_units_quantity'] = product_data_clean['metrics.organic_units_quantity'].fillna(0)

        # Get color for this product (cycle through palette if more products than colors)
        color = color_palette[idx % len(color_palette)]

        # Ads Sales chart - only include points with actual data
        try:
            valid_data = product_data_clean[product_data_clean['metrics.total_amount'] > 0].copy()
            if not valid_data.empty:
                # Double check for NaN values
                valid_data = valid_data.dropna(subset=['date', 'metrics.total_amount'])
                if not valid_data.empty:
                    # Get clean product ID for display
                    clean_id = str(product).replace('MLB', '') if isinstance(product, str) else product
                    product_text = "Produto" if locale == "pt_BR" else "Producto"
                    product_id_display = f"{product_text} {clean_id}"  # Keep this for hover template

                    # Use only the numeric ID for the legend
                    legend_display = clean_id

                    fig_ads_sales.add_trace(go.Scatter(
                        x=valid_data['date'],
                        y=valid_data['metrics.total_amount'],
                        mode='lines+markers',
                        name=legend_display,  # Use only the ID in the legend
                        line=dict(color=color, width=2),
                        marker=dict(color=color, size=8),
                        connectgaps=True,  # Connect gaps caused by missing values
                        hovertemplate=f'<b>%{{x|%d/%m/%Y}}</b><br>{t("sales_text")}: {t("currency_symbol")}%{{y:.2f}}<extra>' + product_id_display + '</extra>'
                    ))
        except Exception as e:
            print(f"Error adding ads sales trace for product {product}: {e}")

        # Organic Sales chart - only include points with actual data AFTER campaign creation
        try:
            # Ensure campaign_creation_datetime is timezone-naive for comparison
            campaign_date_naive = campaign_creation_datetime
            if hasattr(campaign_date_naive, 'tzinfo') and campaign_date_naive.tzinfo is not None:
                campaign_date_naive = campaign_date_naive.tz_localize(None)

            # Convert campaign_date_naive to numpy datetime64 for consistent comparison
            campaign_date_np = np.datetime64(campaign_date_naive)

            # Filter data to only include dates after campaign creation
            # First ensure all dates are valid for comparison
            product_data_clean['date_valid'] = pd.to_datetime(product_data_clean['date'], errors='coerce')

            # Filter using the valid dates column
            valid_organic_data = product_data_clean[
                (product_data_clean['metrics.organic_units_quantity'] > 0) &
                (product_data_clean['date_valid'].notna()) &
                (product_data_clean['date_valid'] >= campaign_date_np)
            ].copy()

            if not valid_organic_data.empty:
                # Double check for NaN values
                valid_organic_data = valid_organic_data.dropna(subset=['date', 'metrics.organic_units_quantity'])
                if not valid_organic_data.empty:
                    # Get clean product ID for display
                    clean_id = str(product).replace('MLB', '') if isinstance(product, str) else product
                    product_text = "Produto" if locale == "pt_BR" else "Producto"
                    product_id_display = f"{product_text} {clean_id}"  # Keep this for hover template

                    # Use only the numeric ID for the legend
                    legend_display = clean_id

                    fig_organic_sales.add_trace(go.Scatter(
                        x=valid_organic_data['date'],
                        y=valid_organic_data['metrics.organic_units_quantity'],
                        mode='lines+markers',
                        name=legend_display,  # Use only the ID in the legend
                        line=dict(color=color, width=2),
                        marker=dict(color=color, size=8),
                        connectgaps=True,  # Connect gaps caused by missing values
                        hovertemplate=f'<b>%{{x|%d/%m/%Y}}</b><br>{t("units_text")}: %{{y}}<extra>' + product_id_display + '</extra>'
                    ))
        except Exception as e:
            print(f"Error adding organic sales trace for product {product}: {e}")

    # Common layout settings for all charts
    chart_height = 750  # Increased height by 1.5x to make charts bigger

 # ...existing code...

    # Different legend settings for different chart types
# Place legend below the chart, no scrolling
    legend_settings_default = dict(
    orientation="h",
    yanchor="top",
    y=-0.25,  # Below the chart
    xanchor="center",
    x=0.5,
    bgcolor="rgba(255, 255, 255, 0.95)",
    bordercolor="rgba(0, 0, 0, 0.1)",
    borderwidth=1,
    font=dict(size=12),
    itemsizing="constant",
    traceorder="normal"
)

    legend_settings_metrics = dict(
        orientation="h",  # Horizontal legend
        yanchor="bottom",
        y=1.22,  # Place legend even higher above the chart (was -0.8)
        xanchor="center",
        x=0.5,  # Center the legend
        bgcolor="rgba(255, 255, 255, 0.95)",
        bordercolor="rgba(0, 0, 0, 0.1)",
        borderwidth=1,
        font=dict(size=11),  # Slightly smaller font
        itemsizing="constant",
        tracegroupgap=5,  # Reduced gap between legend items
        traceorder="normal"  # Keep traces in order
    )

    # Adjusted margins with more TOP space for legend
    margin_settings_default = dict(l=50, r=50, t=120, b=80, pad=10)  # More top margin
    margin_settings_metrics = dict(l=50, r=50, t=170, b=80, pad=10)  # More top margin for CTR/CPC

    # Common chart layout improvements
    def apply_chart_layout(fig, y_axis_title, is_metric_chart=False):
        # Select appropriate legend and margin settings based on chart type
        legend_settings = legend_settings_metrics if is_metric_chart else legend_settings_default
        margin_settings = margin_settings_metrics if is_metric_chart else margin_settings_default

        if len(fig.data) > 0:
            fig.update_layout(
                xaxis=dict(
                    showgrid=True,
                    gridwidth=1,
                    gridcolor='rgba(220, 220, 220, 0.5)',
                    zeroline=False,
                    title=dict(text='', font=dict(size=14))
                ),
                yaxis=dict(
                    showgrid=True,
                    gridwidth=1,
                    gridcolor='rgba(220, 220, 220, 0.5)',
                    zeroline=False,
                    title=dict(text=y_axis_title, font=dict(size=14))
                ),
                plot_bgcolor='rgba(250, 250, 250, 0.5)',
                paper_bgcolor='rgba(0,0,0,0)',
                height=chart_height,
                legend=legend_settings,
                margin=margin_settings,
                hovermode='closest',
                template='plotly_white',
                showlegend=True
            )
        else:
            # Add empty message if no data
            no_data_text = f"Não há dados de {y_axis_title.lower()} disponíveis"
            if locale == "es_AR":
                no_data_text = f"No hay datos de {y_axis_title.lower()} disponibles"
            fig.add_annotation(
                text=no_data_text,
                xref="paper", yref="paper",
                x=0.5, y=0.5, showarrow=False,
                font=dict(size=14)
            )
            fig.update_layout(
                template='plotly_white',
                height=chart_height,
                margin=margin_settings,
                showlegend=True
            )
    # ...existing code...

    # Apply layout to each chart - sales charts use default settings
    apply_chart_layout(fig_ads_sales, t('ads_sales_chart_title'), False)

    # Apply the same layout as other charts for consistency
    apply_chart_layout(fig_organic_sales, t('organic_sales_chart_title'), False)

    ads_sales_html = fig_ads_sales.to_html(full_html=False, include_plotlyjs=False)
    organic_sales_html = fig_organic_sales.to_html(full_html=False, include_plotlyjs=False)

    # Create separate time-series charts for CTR and CPC
    fig_ctr = go.Figure()
    fig_cpc = go.Figure()
    fig_acos = go.Figure()  # New chart for ACOS

    for idx, product in enumerate(active_products):
        product_data = df_active[df_active['item_id'] == product].copy()

        # Skip if product data is completely empty
        if product_data.empty:
            continue

        # Always use product ID for display
        try:
            # Clean up MLB ID for display
            clean_id = str(product).replace('MLB', '') if isinstance(product, str) else product
        except Exception as e:
            print(f"Error getting product name for {product}: {e}")
            # Clean up MLB ID for display
            clean_id = str(product).replace('MLB', '') if isinstance(product, str) else product

        # Make a copy to avoid SettingWithCopyWarning
        product_data_clean = product_data.copy()

        # Check if metrics columns exist, if not try to create them from nested metrics
        if 'metrics' in product_data_clean.columns:
            # Try to extract from nested metrics dictionary
            if 'metrics.ctr' not in product_data_clean.columns:
                product_data_clean['metrics.ctr'] = product_data_clean['metrics'].apply(
                    lambda x: x.get('ctr', 0) if isinstance(x, dict) else 0
                )
            if 'metrics.cpc' not in product_data_clean.columns:
                product_data_clean['metrics.cpc'] = product_data_clean['metrics'].apply(
                    lambda x: x.get('cpc', 0) if isinstance(x, dict) else 0
                )
            if 'metrics.acos' not in product_data_clean.columns:
                product_data_clean['metrics.acos'] = product_data_clean['metrics'].apply(
                    lambda x: x.get('acos', 0) if isinstance(x, dict) else 0
                )
            if 'metrics.clicks' not in product_data_clean.columns:
                product_data_clean['metrics.clicks'] = product_data_clean['metrics'].apply(
                    lambda x: x.get('clicks', 0) if isinstance(x, dict) else 0
                )
            if 'metrics.prints' not in product_data_clean.columns:
                product_data_clean['metrics.prints'] = product_data_clean['metrics'].apply(
                    lambda x: x.get('prints', 0) if isinstance(x, dict) else 0
                )
            if 'metrics.cost' not in product_data_clean.columns:
                product_data_clean['metrics.cost'] = product_data_clean['metrics'].apply(
                    lambda x: x.get('cost', 0) if isinstance(x, dict) else 0
                )

        # Fill NaN values with 0 for better visualization
        product_data_clean['metrics.ctr'] = product_data_clean['metrics.ctr'].fillna(0)
        product_data_clean['metrics.cpc'] = product_data_clean['metrics.cpc'].fillna(0)
        product_data_clean['metrics.acos'] = product_data_clean['metrics.acos'].fillna(0)
        product_data_clean['metrics.clicks'] = product_data_clean['metrics.clicks'].fillna(0)
        product_data_clean['metrics.prints'] = product_data_clean['metrics.prints'].fillna(0)
        product_data_clean['metrics.cost'] = product_data_clean['metrics.cost'].fillna(0)

        # Get color for this product (cycle through palette if more products than colors)
        color = color_palette[idx % len(color_palette)]

        # CTR chart - only include points with actual data (clicks and impressions)
        try:
            valid_ctr_data = product_data_clean[(product_data_clean['metrics.clicks'] > 0) &
                                               (product_data_clean['metrics.prints'] > 0) &
                                               (product_data_clean['metrics.ctr'] > 0)].copy()
            if not valid_ctr_data.empty:
                # Double check for NaN values
                valid_ctr_data = valid_ctr_data.dropna(subset=['date', 'metrics.ctr'])
                if not valid_ctr_data.empty:
                    # Get clean product ID for display
                    clean_id = str(product).replace('MLB', '') if isinstance(product, str) else product
                    product_text = "Produto" if locale == "pt_BR" else "Producto"
                    product_id_display = f"{product_text} {clean_id}"  # Keep this for hover template

                    # Use only the numeric ID for the legend
                    legend_display = clean_id

                    fig_ctr.add_trace(go.Scatter(
                        x=valid_ctr_data['date'],
                        y=valid_ctr_data['metrics.ctr'],
                        mode='lines+markers',
                        name=legend_display,  # Use only the ID in the legend
                        line=dict(color=color, width=2),
                        marker=dict(color=color, size=8),
                        connectgaps=True,  # Connect gaps caused by missing values
                        hovertemplate='<b>%{x|%d/%m/%Y}</b><br>CTR: %{y:.2f}%<extra>' + product_id_display + '</extra>'
                    ))
        except Exception as e:
            print(f"Error adding CTR trace for product {product}: {e}")

        # CPC chart - only include points with actual data (cost and clicks)
        try:
            valid_cpc_data = product_data_clean[(product_data_clean['metrics.cost'] > 0) &
                                               (product_data_clean['metrics.clicks'] > 0) &
                                               (product_data_clean['metrics.cpc'] > 0)].copy()
            if not valid_cpc_data.empty:
                # Double check for NaN values
                valid_cpc_data = valid_cpc_data.dropna(subset=['date', 'metrics.cpc'])
                if not valid_cpc_data.empty:
                    # Get clean product ID for display
                    clean_id = str(product).replace('MLB', '') if isinstance(product, str) else product
                    product_text = "Produto" if locale == "pt_BR" else "Producto"
                    product_id_display = f"{product_text} {clean_id}"  # Keep this for hover template

                    # Use only the numeric ID for the legend
                    legend_display = clean_id

                    fig_cpc.add_trace(go.Scatter(
                        x=valid_cpc_data['date'],
                        y=valid_cpc_data['metrics.cpc'],
                        mode='lines+markers',
                        name=legend_display,  # Use only the ID in the legend
                        line=dict(color=color, width=2),
                        marker=dict(color=color, size=8),
                        connectgaps=True,  # Connect gaps caused by missing values
                        hovertemplate=f'<b>%{{x|%d/%m/%Y}}</b><br>CPC: {t("currency_symbol")}%{{y:.2f}}<extra>' + product_id_display + '</extra>'
                    ))
        except Exception as e:
            print(f"Error adding CPC trace for product {product}: {e}")

        # ACOS chart - only include points with actual data (cost and revenue)
        try:
            valid_acos_data = product_data_clean[(product_data_clean['metrics.cost'] > 0) &
                                                (product_data_clean['metrics.total_amount'] > 0) &
                                                (product_data_clean['metrics.acos'] > 0)].copy()
            if not valid_acos_data.empty:
                # Double check for NaN values
                valid_acos_data = valid_acos_data.dropna(subset=['date', 'metrics.acos'])
                if not valid_acos_data.empty:
                    # Get clean product ID for display
                    clean_id = str(product).replace('MLB', '') if isinstance(product, str) else product
                    product_text = "Produto" if locale == "pt_BR" else "Producto"
                    product_id_display = f"{product_text} {clean_id}"  # Keep this for hover template

                    # Use only the numeric ID for the legend
                    legend_display = clean_id

                    fig_acos.add_trace(go.Scatter(
                        x=valid_acos_data['date'],
                        y=valid_acos_data['metrics.acos'],
                        mode='lines+markers',
                        name=legend_display,  # Use only the ID in the legend
                        line=dict(color=color, width=2),
                        marker=dict(color=color, size=8),
                        connectgaps=True,  # Connect gaps caused by missing values
                        hovertemplate='<b>%{x|%d/%m/%Y}</b><br>ACOS: %{y:.2f}%<extra>' + product_id_display + '</extra>'
                    ))
        except Exception as e:
            print(f"Error adding ACOS trace for product {product}: {e}")

    # Apply layout to CTR, CPC, and ACOS charts - use metric chart settings for these
    apply_chart_layout(fig_ctr, 'CTR (%)', False)
    apply_chart_layout(fig_cpc, f'CPC ({t("currency_symbol")})', False)

    # Special handling for ACOS chart to add benchmark line
    if len(fig_acos.data) > 0:
        # Apply common layout first
        apply_chart_layout(fig_acos, 'ACOS (%)', False)  # ACOS usually has fewer items

        # Add benchmark line to ACOS chart
        try:
            min_date = df_active['date'].min()
            max_date = df_active['date'].max()

            # Only add the shape if we have valid dates
            if pd.notna(min_date) and pd.notna(max_date):
                fig_acos.add_shape(
                    type="line",
                    x0=min_date,
                    y0=acos_benchmark,
                    x1=max_date,
                    y1=acos_benchmark,
                    line=dict(
                        color="red",
                        width=2,
                        dash="dash",
                    )
                )

                # Add annotation for benchmark
                fig_acos.add_annotation(
                    x=max_date,
                    y=acos_benchmark,
                    xref="x",
                    yref="y",
                    text=f"{t('acos_benchmark')}: {acos_benchmark:.1f}%",
                    showarrow=True,
                    arrowhead=2,
                    arrowsize=1,
                    arrowwidth=2,
                    arrowcolor="red",
                    ax=-80,
                    ay=-30,
                    bordercolor="red",
                    borderwidth=2,
                    borderpad=4,
                    bgcolor="white",
                    opacity=0.8
                )
        except Exception as e:
            print(f"Error adding benchmark line to ACOS chart: {e}")
    else:
        # If no data, apply empty layout with default margin settings
        margin_settings = margin_settings_default  # Use default margin settings
        fig_acos.update_layout(
            template='plotly_white',
            height=chart_height,
            margin=margin_settings,
            showlegend=True
        )
        fig_acos.add_annotation(
            text="Não há dados de ACOS disponíveis" if locale == "pt_BR" else "No hay datos de ACOS disponibles",
            xref="paper", yref="paper",
            x=0.5, y=0.5, showarrow=False,
            font=dict(size=14)
        )

    ctr_html = fig_ctr.to_html(full_html=False, include_plotlyjs=False)
    cpc_html = fig_cpc.to_html(full_html=False, include_plotlyjs=False)
    acos_html = fig_acos.to_html(full_html=False, include_plotlyjs=False)

    # Create a pie chart for lost impressions breakdown
    labels = [t('by_insufficient_budget'), t('by_low_ranking')]
    values = [lost_by_budget, lost_by_rank]
    colors = ['#ff9999', '#66b3ff']

    fig_lost_impressions = go.Figure(data=[go.Pie(
        labels=labels,
        values=values,
        hole=.4,
        marker=dict(colors=colors),
        textinfo='percent+label'
    )])

    fig_lost_impressions.update_layout(
        title=t('lost_impressions_chart_title'),
        template='plotly_white',
        height=525,  # Increased height by 1.5x (from 350 to 525)
        margin=dict(l=30, r=30, t=80, b=30, pad=4),  # Compact margins
        showlegend=True
    )

    # Fix the variable name to match what's used in the template and include Plotly library
    lost_impressions_html = fig_lost_impressions.to_html(full_html=False, include_plotlyjs='cdn')

    # Create HTML for lost impressions analysis and budget recommendation
    lost_impressions_section_html = f"""
    <div class="insights-section">
        <h2>{t("lost_impressions")}</h2>

        <div class="alert-container {{'alert-warning' if total_lost > 30 else 'alert-success' if total_lost < 15 else 'alert-info'}}">
            <h3>{t("impression_loss_reasons")}</h3>

            <div class="two-column">
                <div>
                    <div class="chart-item" style="max-height: 525px; overflow: visible;">
                        {lost_impressions_html}
                    </div>
                </div>
                <div>
                    <p>{locale == "pt_BR" and f"Sua campanha está perdendo <strong>{total_lost:.1f}%</strong> das impressões possíveis pelos seguintes motivos:" or f"Tu campaña está perdiendo <strong>{total_lost:.1f}%</strong> de las impresiones posibles por los siguientes motivos:"}</p>

                    <div class="data-row">
                        <span class="data-label">{t("by_insufficient_budget_label")}</span>
                        <span class="data-value-inline"><strong>{lost_by_budget:.1f}%</strong> ({budget_percentage:.1f}% {t("of_losses")})</span>
                    </div>

                    <div class="data-row">
                        <span class="data-label">{t("by_low_ranking_label")}</span>
                        <span class="data-value-inline"><strong>{lost_by_rank:.1f}%</strong> ({rank_percentage:.1f}% {t("of_losses")})</span>
                    </div>

                    <div style="margin-top: 20px;">
                        <p>{locale == "pt_BR" and f"A <strong>Taxa de Impressão</strong> atual da sua campanha é de <strong>{impression_share:.1f}%</strong>, o que significa que seus anúncios estão sendo exibidos em {impression_share:.1f}% das oportunidades possíveis." or f"La <strong>Tasa de Impresión</strong> actual de tu campaña es de <strong>{impression_share:.1f}%</strong>, lo que significa que tus anuncios se están mostrando en el {impression_share:.1f}% de las oportunidades posibles."}</p>
                    </div>

                    {f'''
                    <div class="alert-container alert-success" style="margin-top: 20px;">
                        <h4>{t("well_optimized_campaign")}</h4>
                        <p>{locale == "pt_BR" and f"Parabéns! Sua campanha está perdendo apenas <strong>{total_lost:.1f}%</strong> das impressões possíveis, o que indica uma excelente otimização." or f"¡Felicitaciones! Tu campaña está perdiendo solo <strong>{total_lost:.1f}%</strong> de las impresiones posibles, lo que indica una excelente optimización."}</p>
                        <p>{t("maximizing_visibility")}</p>
                        <ul style="margin-top: 10px; padding-left: 20px;">
                            <li>{t("greater_reach")}</li>
                            <li>{t("better_roi")}</li>
                            <li>{t("higher_sales_potential")}</li>
                        </ul>
                        <div class="tip-box" style="margin-top: 10px; margin-bottom: 0; background-color: #d4edda; border-left: 4px solid #28a745;">
                            <div class="tip-icon">🏆</div>
                            <div class="tip-content">
                                <strong>{t("excellent_work")}</strong> {t("continue_monitoring")}
                            </div>
                        </div>
                    </div>
                    ''' if total_lost < 15 else ''}

                    {f'''
                    <div class="alert-container alert-warning" style="margin-top: 20px;">
                        <h4>{t("budget_recommendation")}</h4>
                        <p>{locale == "pt_BR" and f"Sua campanha está perdendo <strong>{lost_by_budget:.1f}%</strong> das impressões possíveis devido a um orçamento diário insuficiente." or f"Tu campaña está perdiendo <strong>{lost_by_budget:.1f}%</strong> de las impresiones posibles debido a un presupuesto diario insuficiente."}</p>
                        <p>{locale == "pt_BR" and f"Recomendamos aumentar o orçamento diário atual de <strong>{t('currency_symbol')}{campaign_budget:,.2f}</strong> para melhorar a visibilidade dos seus anúncios." or f"Recomendamos aumentar el presupuesto diario actual de <strong>{t('currency_symbol')}{campaign_budget:,.2f}</strong> para mejorar la visibilidad de tus anuncios."}</p>
                        <div class="tip-box" style="margin-top: 10px; margin-bottom: 0;">
                            <div class="tip-icon">💡</div>
                            <div class="tip-content">
                                <strong>{t("tip")}:</strong> {t("budget_tip")}
                            </div>
                        </div>
                    </div>
                    ''' if lost_by_budget > 30 else ''}

                    {f'''
                    <div class="alert-container alert-info" style="margin-top: 20px;">
                        <h4>{t("ad_ranking")}</h4>
                        <p>{locale == "pt_BR" and f"Sua campanha está perdendo <strong>{lost_by_rank:.1f}%</strong> das impressões possíveis devido à falta ou baixa classificação dos anúncios." or f"Tu campaña está perdiendo <strong>{lost_by_rank:.1f}%</strong> de las impresiones posibles debido a la falta o baja clasificación de los anuncios."}</p>
                        </ul>
                    </div>
                    ''' if lost_by_rank > 20 else ''}
                </div>
            </div>
        </div>
    </div>
    """

    # We don't need a separate budget recommendation anymore as it's included in the lost_impressions_section_html

    # Fetch purchase experience data if access token is provided and there are lost impressions by rank
    purchase_experience_html = ""
    if access_token and lost_by_rank > 20:  # Only fetch if there's a problem with rank
        print("Access token provided and lost impressions by rank > 30%, fetching purchase experience data")
        try:
            # Get item IDs from active products
            item_ids = df_active['item_id'].unique().tolist()
            print(f"Found {len(item_ids)} active products to fetch purchase experience data")

            # Fetch purchase experience data for all products
            purchase_data_raw = await fetch_purchase_experience_batch(access_token, item_ids, locale)
            print(f"Fetched purchase experience data for {len(purchase_data_raw)} products")

            # Process the data
            purchase_data_processed = {}
            for item_id, data in purchase_data_raw.items():
                # Process all data regardless of metrics_details presence
                if data.get('metrics_details', {}).get("problems"):
                    processed = process_purchase_experience_data(data)
                    if processed:
                        purchase_data_processed[item_id] = processed
                        print(f"Processed purchase experience data for {item_id}")

            # Generate HTML for purchase experience
            if purchase_data_processed:
                print(f"Generating purchase experience HTML for {len(purchase_data_processed)} products")
                purchase_experience_html = create_purchase_experience_html(purchase_data_processed, df_active, locale)
                print(f"Purchase experience HTML length: {len(purchase_experience_html)}")
            else:
                print("No purchase experience data processed, skipping HTML generation")
        except Exception as e:
            print(f"Error fetching purchase experience data: {e}")
            purchase_experience_html = ""







    # Create a new barplot for listing types and conditions
    listing_types = {}
    conditions = {}

    for product in campaign_products:
        listing_type = product.get('listing_type_id', 'N/A')
        condition_type = product.get('condition', 'N/A')

        if listing_type in listing_types:
            listing_types[listing_type] += 1
        else:
            listing_types[listing_type] = 1

        if condition_type in conditions:
            conditions[condition_type] += 1
        else:
            conditions[condition_type] = 1



    sanitized_title = re.sub(r'[^\w\s-]', '', campaign_title)
    sanitized_title = re.sub(r'[-\s]+', '_', sanitized_title).strip('-_')

    # Shorten title if too long
    if len(sanitized_title) > 30:
        sanitized_title = sanitized_title[:30]

    # If title is empty after sanitizing, use a default name
    if not sanitized_title:
        sanitized_title = 'campaign'

    # Set HTML language based on locale
    html_lang = "pt-BR" if locale == "pt_BR" else "es-AR" if locale == "es_AR" else "pt-BR"

    # Generate HTML content with proper localization
    html_content = f"""
    <!DOCTYPE html>
    <html lang="{html_lang}">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>{t("dashboard_title")} - {campaign_title}</title>
        <link rel="preconnect" href="https://fonts.googleapis.com">
        <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
        <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
        <style>
            /* Optimize for single-page PDF */
            @page {{
                size: auto;
                margin: 0;
            }}
            body {{
                /* Prevent page breaks inside elements */
                page-break-inside: avoid;
            }}
            .chart-container, .insights-section, .summary-cards {{
                page-break-inside: avoid;
            }}
            :root {{
                --primary-color: #4a90e2; /* Brighter Blue */
                --secondary-color: #50e3c2; /* Teal */
                --accent-color: #6c5ce7; /* Purple accent */
                --text-color: #2d3436;
                --text-light: #636e72;
                --text-muted: #b2bec3;
                --bg-color: #f7f9fc; /* Lighter background */
                --card-bg: #ffffff;
                --border-color: #e1e4e8;
                --shadow-color: rgba(0, 0, 0, 0.08);
                --success-bg: #d4edda;
                --success-border: #c3e6cb;
                --success-text: #155724;
                --warning-bg: #fff3cd;
                --warning-border: #ffeeba;
                --warning-text: #856404;
                --danger-bg: #f8d7da;
                --danger-border: #f5c6cb;
                --danger-text: #721c24;
                --info-bg: #d1ecf1;
                --info-border: #bee5eb;
                --info-text: #0c5460;

                /* Animation durations */
                --transition-fast: 0.2s;
                --transition-medium: 0.3s;
                --transition-slow: 0.5s;
            }}

            body {{
                font-family: 'Inter', sans-serif;
                margin: 0;
                padding: 25px;
                background-color: var(--bg-color);
                color: var(--text-color);
                line-height: 1.6;
                transition: background-color var(--transition-medium) ease;
            }}
            .container {{
                max-width: 1300px; /* Slightly wider */
                margin: 20px auto;
                background-color: var(--card-bg);
                padding: 30px; /* More padding */
                border-radius: 16px; /* Softer corners */
                box-shadow: 0 10px 30px var(--shadow-color);
                border: 1px solid var(--border-color);
                transition: all var(--transition-medium) ease;
            }}
            h1, h2, h3 {{
                color: var(--text-color);
                margin-bottom: 0.8em;
                font-weight: 600;
            }}
            h1 {{ text-align: center; font-size: 2em; margin-bottom: 1.2em; }}
            h2 {{ font-size: 1.5em; border-bottom: 1px solid var(--border-color); padding-bottom: 0.4em; margin-top: 1.5em; }}
            h3 {{ font-size: 1.2em; color: var(--primary-color); font-weight: 500; }}
            h4 {{ font-size: 1.1em; color: var(--text-light); font-weight: 500; margin-bottom: 0.6em; }}

            .summary-cards {{
                display: grid;
                grid-template-columns: repeat(auto-fill, minmax(180px, 1fr)); /* Adjusted minmax */
                gap: 20px;
                margin-bottom: 30px;
            }}
            .summary-card {{
                background-color: var(--card-bg);
                border-radius: 12px;
                padding: 22px;
                box-shadow: 0 8px 16px var(--shadow-color);
                border: 1px solid var(--border-color);
                transition: transform var(--transition-medium) ease, box-shadow var(--transition-medium) ease;
                border-left: 4px solid var(--primary-color);
                position: relative;
                overflow: hidden;
            }}
            .summary-card::before {{
                content: "";
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0) 100%);
                z-index: 1;
            }}
            .summary-card:hover {{
                transform: translateY(-5px);
                box-shadow: 0 12px 24px rgba(0, 0, 0, 0.12);
            }}
            .card-title {{
                font-size: 0.9em; /* Slightly smaller */
                color: var(--text-muted);
                margin-top: 0;
                margin-bottom: 8px; /* Space between title and value */
                text-transform: uppercase;
                font-weight: 500;
            }}
            .card-value {{
                font-size: 1.8em; /* Adjusted size */
                font-weight: 600;
                margin-bottom: 0;
                color: var(--text-color);
                line-height: 1.2;
            }}

            .chart-container {{
                margin-bottom: 40px;
                padding: 25px;
                background-color: var(--card-bg);
                border-radius: 12px;
                box-shadow: 0 8px 20px var(--shadow-color);
                border: 1px solid var(--border-color);
                transition: transform var(--transition-medium) ease, box-shadow var(--transition-medium) ease;
            }}
            .chart-container:hover {{
                transform: translateY(-5px);
                box-shadow: 0 12px 30px rgba(0, 0, 0, 0.12);
            }}
            .chart-container h2 {{
                font-size: 1.4em;
                color: var(--text-color);
                margin-bottom: 20px;
                text-align: left;
                border-bottom: 2px solid #f0f4f8;
                padding-bottom: 10px;
                position: relative;
            }}
            .chart-container h2::after {{
                content: "";
                position: absolute;
                bottom: -2px;
                left: 0;
                width: 80px;
                height: 2px;
                background-color: var(--primary-color);
            }}

            /* Purchase Experience Styles */
            .purchase-experience-container {{
                background: linear-gradient(to bottom, #ffffff, #f9fbff);
                border-radius: 12px;
                box-shadow: 0 8px 30px rgba(0, 0, 0, 0.08);
                border: none;
                overflow: hidden;
            }}

            .purchase-experience-container h2 {{
                color: #2c3e50;
                font-size: 1.5em;
                margin-bottom: 20px;
                border-bottom: 2px solid #e7eef7;
                padding-bottom: 12px;
            }}

            .info-box, .tip-box {{
                display: flex;
                align-items: flex-start;
                margin-bottom: 25px;
                padding: 15px;
                border-radius: 8px;
                background-color: #f1f8ff;
                border-left: 4px solid #4a90e2;
                transition: transform 0.2s ease, box-shadow 0.2s ease;
            }}

            .tip-box {{
                margin-top: 25px;
                background-color: #fffbf1;
                border-left: 4px solid #f0ad4e;
            }}

            .info-box:hover, .tip-box:hover {{
                transform: translateY(-2px);
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
            }}

            .info-icon, .tip-icon {{
                font-size: 1.5em;
                margin-right: 15px;
                line-height: 1;
            }}

            .info-content, .tip-content {{
                flex: 1;
                font-size: 0.95em;
                line-height: 1.5;
            }}

            .product-cards {{
                display: grid;
                grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
                gap: 20px;
                margin: 25px 0;
            }}

            .product-card {{
                background-color: white;
                border-radius: 10px;
                box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
                padding: 20px;
                transition: transform 0.3s ease, box-shadow 0.3s ease;
                border-top: 4px solid #e0e0e0;
            }}

            .product-card.status-success {{
                border-top-color: #28a745;
            }}

            .product-card.status-warning {{
                border-top-color: #ffc107;
            }}

            .product-card.status-danger {{
                border-top-color: #dc3545;
            }}

            .product-card:hover {{
                transform: translateY(-5px);
                box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
            }}

            .product-header {{
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 15px;
                padding-bottom: 10px;
                border-bottom: 1px solid #f0f0f0;
            }}

            .product-name {{
                font-size: 1.1em;
                font-weight: 600;
                color: #2c3e50;
                margin: 0;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
                max-width: 70%;
            }}

            .status-badge {{
                padding: 4px 8px;
                border-radius: 20px;
                font-size: 0.7em;
                font-weight: 700;
                text-transform: uppercase;
                color: white;
            }}

            .status-badge.status-success {{
                background-color: #28a745;
            }}

            .status-badge.status-warning {{
                background-color: #ffc107;
                color: #212529;
            }}

            .status-badge.status-danger {{
                background-color: #dc3545;
            }}

            .product-rating {{
                display: flex;
                flex-direction: column;
                align-items: center;
                margin-bottom: 15px;
                padding: 10px;
                background-color: #f8f9fa;
                border-radius: 8px;
            }}

            .rating-label {{
                font-size: 0.8em;
                color: #6c757d;
                margin-bottom: 5px;
            }}

            .rating-value {{
                font-size: 1.8em;
                font-weight: 700;
                color: #2c3e50;
            }}

            .product-metrics h4 {{
                font-size: 0.9em;
                color: #6c757d;
                margin-bottom: 10px;
                text-transform: uppercase;
                letter-spacing: 0.5px;
            }}

            .metrics-list {{
                list-style-type: none;
                padding: 0;
                margin: 0;
            }}

            .metrics-list li {{
                display: flex;
                justify-content: space-between;
                padding: 8px 0;
                border-bottom: 1px dashed #e0e0e0;
                font-size: 0.9em;
                position: relative;
                padding-left: 15px;
            }}

            .metrics-list li:last-child {{
                border-bottom: none;
            }}

            .metrics-list li:before {{
                content: "•";
                position: absolute;
                left: 0;
                top: 8px;
            }}

            .metrics-list li.metric-success:before {{
                color: #28a745;
            }}

            .metrics-list li.metric-warning:before {{
                color: #ffc107;
            }}

            .metrics-list li.metric-danger:before {{
                color: #dc3545;
            }}

            .metric-value {{
                font-weight: 600;
                color: #2c3e50;
            }}

            @media (max-width: 768px) {{
                .product-cards {{
                    grid-template-columns: 1fr;
                }}
            }}

            .alert-container {{
                padding: 20px;
                margin: 25px 0;
                border-radius: 12px;
                border: 1px solid transparent;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
                transition: transform var(--transition-fast) ease, box-shadow var(--transition-fast) ease;
            }}
            .alert-container:hover {{
                transform: translateY(-3px);
                box-shadow: 0 6px 15px rgba(0, 0, 0, 0.08);
            }}
            .alert-info {{ background-color: var(--info-bg); border-color: var(--info-border); color: var(--info-text); }}
            .alert-warning {{ background-color: var(--warning-bg); border-color: var(--warning-border); color: var(--warning-text); }}
            .alert-danger {{ background-color: var(--danger-bg); border-color: var(--danger-border); color: var(--danger-text); }}
            .alert-success {{ background-color: var(--success-bg); border-color: var(--success-border); color: var(--success-text); }}
            .alert-container h3 {{ color: inherit; font-size: 1.2em; margin-top: 0; margin-bottom: 0.8em; font-weight: 600; }}
            .alert-container h4 {{ color: inherit; font-size: 1.1em; margin-top: 1em; margin-bottom: 0.6em; font-weight: 500; }}
            .alert-container p {{ margin-bottom: 0.7em; line-height: 1.5; }}
            .alert-container p:last-child {{ margin-bottom: 0; }}

            .insights-section {{
                margin: 30px 0;
                padding: 25px;
                background-color: var(--card-bg);
                border-radius: 12px;
                box-shadow: 0 8px 20px var(--shadow-color);
                border: 1px solid var(--border-color);
                transition: transform var(--transition-medium) ease, box-shadow var(--transition-medium) ease;
            }}
            .insights-section:hover {{
                transform: translateY(-5px);
                box-shadow: 0 12px 30px rgba(0, 0, 0, 0.12);
            }}
            .insights-section h2 {{
                font-size: 1.4em;
                color: var(--text-color);
                margin-bottom: 20px;
                text-align: left;
                border-bottom: 2px solid #f0f4f8;
                padding-bottom: 10px;
                position: relative;
            }}
            .insights-section h2::after {{
                content: "";
                position: absolute;
                bottom: -2px;
                left: 0;
                width: 80px;
                height: 2px;
                background-color: var(--primary-color);
            }}

            .two-column {{
                display: grid;
                grid-template-columns: 1fr 1fr;
                gap: 25px;
            }}
            @media (max-width: 800px) {{
                .two-column {{ grid-template-columns: 1fr; }}
            }}

            .data-box {{
                background-color: var(--card-bg);
                padding: 15px;
                border-radius: 10px;
                box-shadow: 0 3px 8px var(--shadow-color);
                border: 1px solid var(--border-color);
                margin-bottom: 15px;
                transition: transform var(--transition-fast) ease, box-shadow var(--transition-fast) ease;
            }}
            .data-box:hover {{
                transform: translateY(-2px);
                box-shadow: 0 5px 12px rgba(0, 0, 0, 0.08);
            }}
            .data-title {{ font-size: 0.9em; color: var(--text-muted); margin: 0 0 5px 0; font-weight: 500; text-transform: uppercase; letter-spacing: 0.5px; }}
            .data-value {{ font-size: 1.5em; font-weight: 600; color: var(--text-color); margin: 0 0 5px 0; }}
            .data-description {{ font-size: 0.85em; color: var(--text-light); margin: 0; }}

            .metric-card {{
                background-color: var(--card-bg);
                border-radius: 8px;
                padding: 20px;
                box-shadow: 0 2px 4px var(--shadow-color);
                border: 1px solid var(--border-color);
                margin-bottom: 15px;
            }}
            .metric-card h4 {{
                color: var(--primary-color);
                margin-top: 0;
                margin-bottom: 15px;
                font-size: 1.1em;
                border-bottom: 1px solid var(--border-color);
                padding-bottom: 8px;
            }}

            .data-row {{
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 10px;
                padding-bottom: 5px;
                border-bottom: 1px dashed var(--border-color); /* Subtle separator */
            }}
             .data-row:last-child {{
                border-bottom: none;
                margin-bottom: 0;
                padding-bottom: 0;
            }}
            .data-label {{ color: var(--text-light); font-size: 0.9em; }}
            .data-value-inline {{ font-weight: 600; color: var(--text-color); font-size: 0.95em; text-align: right; }}

            .sub-section {{
                margin-top: 15px;
                margin-left: 0; /* Remove indent */
                padding-top: 15px;
                border-top: 1px solid var(--border-color); /* Use top border instead of left */
            }}
            .sub-section h4 {{ font-size: 1em; color: var(--text-light); border-bottom: none; margin-bottom: 10px; }}

            .campaign-info {{
                background: linear-gradient(135deg, #eef5fc 0%, #f5f9ff 100%);
                border-radius: 16px;
                padding: 30px;
                margin-bottom: 35px;
                border: none;
                box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
                position: relative;
                overflow: hidden;
            }}
            .campaign-info::before {{
                content: "";
                position: absolute;
                top: 0;
                right: 0;
                width: 150px;
                height: 150px;
                background: radial-gradient(circle, rgba(74, 144, 226, 0.1) 0%, rgba(255, 255, 255, 0) 70%);
                z-index: 0;
            }}
            .campaign-info h2 {{
                color: #004085;
                margin-top: 0;
                text-align: left;
                border-bottom: none;
                font-size: 1.6em;
                position: relative;
                z-index: 1;
            }}
            .campaign-info h3 {{
                color: #0056b3;
                margin-top: 1.5em;
                font-weight: 600;
                position: relative;
                z-index: 1;
            }}

            .badge {{
                display: inline-block;
                padding: 4px 10px; /* Adjusted padding */
                border-radius: 12px; /* Pill shape */
                font-size: 0.75em; /* Smaller font */
                font-weight: 600; /* Bolder */
                text-transform: uppercase;
                margin-right: 5px;
                line-height: 1.4;
            }}
            /* Define badge colors using CSS variables for consistency */
            .badge-primary {{ background-color: var(--primary-color); color: white; }}
            .badge-success {{ background-color: var(--success-text); color: white; }} /* Use text color for bg */
            .badge-warning {{ background-color: var(--warning-text); color: white; }} /* Use text color for bg */
            .badge-info {{ background-color: var(--info-text); color: white; }} /* Use text color for bg */
            .badge-danger {{ background-color: var(--danger-text); color: white; }}

            .progress {{
                height: 10px; /* Slightly thicker */
                border-radius: 5px;
                background-color: #e9ecef;
                margin: 8px 0 15px 0; /* More margin */
                overflow: hidden; /* Ensure inner bar respects radius */
            }}
            .progress-bar {{
                height: 100%;
                border-radius: 5px;
                transition: width 0.5s ease-in-out; /* Smooth transition */
            }}

            .lost-impression-table {{
                margin-top: 25px;
                overflow-x: auto; /* Keep horizontal scroll for small screens */
                border: 1px solid var(--border-color);
                border-radius: 8px;
                box-shadow: 0 2px 4px var(--shadow-color);
            }}
            .lost-impression-table table {{
                width: 100%;
                border-collapse: collapse; /* Important for styling */
                border-spacing: 0; /* Remove default spacing */
            }}
            .lost-impression-table th,
            .lost-impression-table td {{
                padding: 12px 15px; /* Consistent padding */
                text-align: left;
                border-bottom: 1px solid var(--border-color);
            }}

            /* Allow product names to wrap */
            .lost-impression-table td:first-child {{
                white-space: normal; /* Allow wrapping for product names */
                word-wrap: break-word;
                max-width: 300px; /* Limit width to force wrapping */
            }}

            /* Keep other cells nowrap */
            .lost-impression-table td:not(:first-child) {{
                white-space: nowrap; /* Prevent wrapping in numeric cells */
            }}
             .lost-impression-table th:nth-child(n+2), /* Center align numeric columns */
             .lost-impression-table td:nth-child(n+2) {{
                text-align: center;
             }}
            .lost-impression-table th {{
                background-color: #f8f9fa; /* Light header */
                font-weight: 600;
                color: var(--text-light);
                text-transform: uppercase;
                font-size: 0.85em;
                border-bottom-width: 2px; /* Thicker bottom border for header */
            }}
            .lost-impression-table tr {{
                 transition: background-color 0.15s ease;
            }}
            .lost-impression-table tbody tr:last-child td {{
                border-bottom: none; /* Remove border from last row */
            }}
            .lost-impression-table tbody tr:hover {{
                background-color: #f1f5f9; /* Subtle hover effect */
            }}
            /* Keep specific row colors but use variables */
            .lost-impression-table tr[style*="background-color: #d4edda"] {{ background-color: var(--success-bg) !important; }}
            .lost-impression-table tr[style*="background-color: #d1ecf1"] {{ background-color: var(--info-bg) !important; }}
            .lost-impression-table tr[style*="background-color: #fff3cd"] {{ background-color: var(--warning-bg) !important; }}
            .lost-impression-table tr[style*="background-color: #f8f9fa"] {{ background-color: #f8f9fa !important; }} /* Keep default */

            /* Utility class for summary below table */
            .table-summary {{
                 margin-top: 15px;
                 padding: 15px;
                 background-color: #f8f9fa;
                 border-radius: 5px;
                 font-size: 0.9em;
                 border: 1px solid var(--border-color);
            }}
            .table-summary strong {{ color: var(--text-color); }}

            /* Chart grid layout */
            .charts-grid {{
                display: grid;
                grid-template-columns: repeat(2, 1fr);
                gap: 20px;
            }}
            @media (max-width: 900px) {{
                .charts-grid {{
                    grid-template-columns: 1fr;
                }}
            }}
            .chart-item {{
                background-color: #fff;
                border-radius: 8px;
                box-shadow: 0 2px 8px rgba(0,0,0,0.05);
                padding: 15px;
                border: 1px solid #eaeaea;
                max-height: 900px; /* Increased by 1.5x from 600px to 900px */
                overflow-y: auto; /* Make charts scrollable vertically */
                position: relative; /* For proper scrolling */
            }}

            /* Make chart legends scrollable if they have many items */
            .js-plotly-plot .legend {{
                overflow-x: hidden !important;
                max-height: none !important;
                flex-wrap: wrap !important;
                overflow-y: visible !important;
            }}

            /* Position product names below the chart */
            .js-plotly-plot .legend .traces {{
                margin-top: 10px !important;
            }}
            .chart-item h3 {{
                font-size: 1.1em;
                margin-top: 0;
                margin-bottom: 10px;
                color: #333;
                text-align: center;
                position: sticky; /* Keep title visible when scrolling */
                top: 0;
                background-color: #fff;
                padding: 5px 0;
                z-index: 10;
            }}
            /* Style for the scrollbar */
            .chart-item::-webkit-scrollbar {{
                width: 8px;
            }}
            .chart-item::-webkit-scrollbar-track {{
                background: #f1f1f1;
                border-radius: 4px;
            }}
            .chart-item::-webkit-scrollbar-thumb {{
                background: #c1c1c1;
                border-radius: 4px;
            }}
            .chart-item::-webkit-scrollbar-thumb:hover {{
                background: #a8a8a8;
            }}
        </style>
    </head>
    <body>
        <div class="container">
            <h1>{t("dashboard_title")} - {campaign_title}</h1>

            <div class="campaign-info">
                <h2>{t("campaign_info")}</h2>
                <div class="two-column">
                    <div>
                        <div class="data-row">
                            <span class="data-label">{t("campaign_id")}:</span>
                            <span class="data-value-inline">{campagin_id}</span>
                        </div>
                        <div class="data-row">
                            <span class="data-label">{t("campaign_title")}:</span>
                            <span class="data-value-inline">{campaign_title}</span>
                        </div>
                        <div class="data-row">
                            <span class="data-label">{t("campaign_strategy")}:</span>
                            <span class="data-value-inline">
                                <span class="badge {{'badge-success' if campaign_strategy == 'PROFITABILITY' else 'badge-warning' if campaign_strategy == 'INCREASE' else 'badge-info' if campaign_strategy == 'VISIBILITY' else 'badge-primary'}}">
                                    {campaign_strategy}
                                </span>
                            </span>
                        </div>

                    </div>
                    <div>
                        <div class="data-row">
                            <span class="data-label">{t("daily_budget")}:</span>
                            <span class="data-value-inline">{t("currency_symbol")}{campaign_budget:,.2f}</span>
                        </div>
                        <div class="data-row">
                            <span class="data-label">{t("creation_date")}:</span>
                            <span class="data-value-inline">{campaign_creation_date}</span>
                        </div>
                        <div class="data-row">
                            <span class="data-label">{t("last_update")}:</span>
                            <span class="data-value-inline">{campaign_last_updated}</span>
                        </div>
                    </div>
                </div>

                <!-- ACOS information moved to Insights section for consistency -->
            </div>

            <div class="summary-cards">
                <div class="summary-card">
                    <h3 class="card-title">{t("total_clicks")}</h3>
                    <p class="card-value">{total_clicks:,}</p>
                </div>
                <div class="summary-card">
                    <h3 class="card-title">{t("total_impressions")}</h3>
                    <p class="card-value">{total_impressions:,}</p>
                </div>
                <div class="summary-card">
                    <h3 class="card-title">{t("total_cost")}</h3>
                    <p class="card-value">{t("currency_symbol")}{total_cost:,.2f}</p>
                </div>
                <div class="summary-card">
                    <h3 class="card-title">{t("total_revenue")}</h3>
                    <p class="card-value">{t("currency_symbol")}{total_revenue:,.2f}</p>
                </div>
                <div class="summary-card">
                    <h3 class="card-title">{t("avg_ctr")}</h3>
                    <p class="card-value">{avg_ctr:.2f}%</p>
                </div>
                <div class="summary-card">
                    <h3 class="card-title">{t("avg_roas")}</h3>
                    <p class="card-value">{avg_roas:.2f}</p>
                </div>
                <div class="summary-card">
                    <h3 class="card-title">{t("acos")}</h3>
                    <p class="card-value">{overall_acos:.2f}%</p>
                </div>
                <div class="summary-card">
                    <h3 class="card-title">{t("cvr")}</h3>
                    <p class="card-value">{avg_cvr:.2f}%</p>
                </div>
                <div class="summary-card">
                    <h3 class="card-title">{t("share_of_voice")}</h3>
                    <p class="card-value">{sov:.2f}%</p>
                </div>
                <div class="summary-card">
                    <h3 class="card-title">{t("total_units")}</h3>
                    <p class="card-value">{units_quantity}</p>
                </div>
            </div>

            <div class="insights-section">
                <h2>{t("sales_comparison")}</h2>
                {before_after_sales_html.replace("<div style='margin-top: 15px; padding: 10px; background-color: #f8f9fa; border-radius: 5px;'>", "<div class='table-summary'>")}
            </div>


            <div class="insights-section">
                <h2>{t("campaign_insights")}</h2>

                <div class="alert-container {{'alert-success' if 'Abaixo' in acos_status else 'alert-warning' if 'Próximo' in acos_status else 'alert-danger' if 'Acima' in acos_status else 'alert-info'}}">
                    <h3>{t("acos_status")}</h3>
                    <p>{t("acos_current_value")}: <strong>{overall_acos:.2f}%</strong>{f" | {t('acos_benchmark_value')}: {acos_benchmark:.2f}%" if acos_benchmark is not None and acos_benchmark > 0 else ""}</p>
                    <p><strong>{acos_status}</strong></p>
                    <p>{t("acos_explanation")}</p>

                    <div style="margin-top: 15px;">
                        <h4>{t("acos_targets")}</h4>
                        <div class="two-column">
                            <div>
                                <div class="data-row">
                                    <span class="data-label">{t("acos_benchmark")}:</span>
                                    <span class="data-value-inline">{acos_benchmark:.2f}%</span>
                                </div>
                                <div class="data-row">
                                    <span class="data-label">{t("acos_target")}:</span>
                                    <span class="data-value-inline">{acos_target:.2f}%</span>
                                </div>
                            </div>
                            <div>
                                {f'<div class="data-row"><span class="data-label">{t("acos_top_search_target")}:</span><span class="data-value-inline">{acos_top_search_target:.2f}%</span></div>' if acos_top_search_target else ''}
                            </div>
                        </div>

                        {f"""
                        <div class="tip-box" style="margin-top: 15px;">
                            <div class="tip-icon">💡</div>
                            <div class="tip-content">
                                <strong>{t("recommendation")}:</strong> {acos_target_recommendation}
                            </div>
                        </div>
                        """ if acos_target_recommendation else ""}
                    </div>
                </div>

                <div class="alert-container alert-info">
                    <h3>{t("impression_visibility_metrics")}</h3>
                    <div class="two-column">
                        <div>
                            <div class="data-box">
                                <p class="data-title">{t("impression_statistics")}</p>
                                <div style="margin-top: 15px;">
                                    <div style="margin-bottom: 15px;">
                                        <p style="font-weight: 500; margin-bottom: 5px;">{t("impression_rate")}</p>
                                        <p style="font-size: 1.2em; font-weight: bold; margin: 5px 0;">{impression_share:.1f}%</p>
                                        <p style="font-size: 0.9em; color: #666;">{t("impression_rate_explanation")}</p>
                                    </div>
                                    <div style="margin-bottom: 15px;">
                                        <p style="font-weight: 500; margin-bottom: 5px;">{t("top_impression_rate")}</p>
                                        <p style="font-size: 1.2em; font-weight: bold; margin: 5px 0;">{top_impression_share:.1f}%</p>
                                        <p style="font-size: 0.9em; color: #666;">{t("top_impression_explanation")}</p>
                                    </div>
                                    <div>
                                        <p style="font-weight: 500; margin-bottom: 5px;">{t("total_lost_impressions")}</p>
                                        <p style="font-size: 1.2em; font-weight: bold; margin: 5px 0;">{total_lost:.1f}%</p>
                                        <p style="font-size: 0.9em; color: #666;">{t("lost_impression_explanation")}</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div>
                            <div class="data-box">
                                <p class="data-title">{t("impression_loss_reasons")}</p>
                                <div style="margin-top: 15px;">
                                    <div style="margin-bottom: 15px;">
                                        <p style="font-weight: 500; margin-bottom: 5px;">{t("insufficient_budget")}</p>
                                        <p style="font-size: 1.2em; font-weight: bold; margin: 5px 0;">{lost_by_budget:.1f}%</p>
                                        <p style="font-size: 0.9em; color: #666;">({budget_percentage:.1f}% {t("of_total_losses")})</p>
                                    </div>
                                    <div>
                                        <p style="font-weight: 500; margin-bottom: 5px;">{t("low_ranking")}</p>
                                        <p style="font-size: 1.2em; font-weight: bold; margin: 5px 0;">{lost_by_rank:.1f}%</p>
                                        <p style="font-size: 0.9em; color: #666;">({rank_percentage:.1f}% {t("of_total_losses")})</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="alert-container alert-success">
                    <h3>{t("detailed_sales_metrics")}</h3>

                    <div class="two-column">
                        <div class="metric-card">
                            <h4>{t("organic_sales")}</h4>
                            <div class="data-row">
                                <span class="data-label">{t("products_quantity")}:</span>
                                <span class="data-value-inline">{organic_items_quantity} {t("items_text")}</span>
                            </div>
                            <div class="data-row">
                                <span class="data-label">{t("units_quantity")}:</span>
                                <span class="data-value-inline">{organic_units_quantity} {t("units_text")}</span>
                            </div>
                            <div class="data-row">
                                <span class="data-label">{t("total_value")}:</span>
                                <span class="data-value-inline">{t("currency_symbol")}{organic_units_amount:,.2f}</span>
                            </div>
                        </div>

                        <div class="metric-card">
                            <h4>{t("ads_sales")}</h4>
                            <div class="data-row">
                                <span class="data-label">{t("total_ads_sales")}:</span>
                                <span class="data-value-inline">{advertising_items_quantity} {t("items_text")}</span>
                            </div>

                            <div class="sub-section">
                                <h4>{t("direct_sales")}</h4>
                                <div class="data-row">
                                    <span class="data-label">{t("products_quantity")}:</span>
                                    <span class="data-value-inline">{direct_items_quantity} {t("items_text")}</span>
                                </div>
                                <div class="data-row">
                                    <span class="data-label">{t("units_quantity")}:</span>
                                    <span class="data-value-inline">{direct_units_quantity} {t("units_text")}</span>
                                </div>
                                <div class="data-row">
                                    <span class="data-label">{t("total_value")}:</span>
                                    <span class="data-value-inline">{t("currency_symbol")}{direct_amount:,.2f}</span>
                                </div>
                            </div>

                            <div class="sub-section">
                                <h4>{t("indirect_sales")}</h4>
                                <div class="data-row">
                                    <span class="data-label">{t("products_quantity")}:</span>
                                    <span class="data-value-inline">{indirect_items_quantity} {t("items_text")}</span>
                                </div>
                                <div class="data-row">
                                    <span class="data-label">{t("units_quantity")}:</span>
                                    <span class="data-value-inline">{indirect_units_quantity} {t("units_text")}</span>
                                </div>
                                <div class="data-row">
                                    <span class="data-label">{t("total_value")}:</span>
                                    <span class="data-value-inline">{t("currency_symbol")}{indirect_amount:,.2f}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            {lost_impressions_section_html}
            {purchase_experience_html}
            <div class="insights-section">
                <h2>{t("performance_charts")}</h2>

                <div class="charts-grid" style="grid-template-columns: 1fr; gap: 60px;">
                    <div class="chart-item" style="padding: 25px 25px 225px 25px; box-shadow: 0 4px 15px rgba(0,0,0,0.08); border-radius: 12px; max-height: none; overflow: visible;">
                        <h3 style="font-size: 1.3em; margin-bottom: 20px; color: #4a90e2;">{t("ads_sales_chart")}</h3>
                        {ads_sales_html}
                    </div>

                    <div class="chart-item" style="padding: 25px 25px 225px 25px; box-shadow: 0 4px 15px rgba(0,0,0,0.08); border-radius: 12px; max-height: none; overflow: visible;">
                        <h3 style="font-size: 1.3em; margin-bottom: 20px; color: #4a90e2;">{t("organic_sales_chart")}</h3>
                        {organic_sales_html}
                    </div>

                    <div class="chart-item" style="padding: 25px 25px 225px 25px; box-shadow: 0 4px 15px rgba(0,0,0,0.08); border-radius: 12px; max-height: none; overflow: visible;">
                        <h3 style="font-size: 1.3em; margin-bottom: 20px; color: #4a90e2;">{t("acos_over_time")}</h3>
                        {acos_html}
                    </div>

                    <div class="chart-item" style="padding: 25px 25px 330px 25px; box-shadow: 0 4px 15px rgba(0,0,0,0.08); border-radius: 12px; max-height: none; overflow: visible;">
                        <h3 style="font-size: 1.3em; margin-bottom: 20px; color: #4a90e2;">{t("ctr_chart")}</h3>
                        {ctr_html}
                    </div>

                    <div class="chart-item" style="padding: 25px 25px 330px 25px; box-shadow: 0 4px 15px rgba(0,0,0,0.08); border-radius: 12px; max-height: none; overflow: visible;">
                        <h3 style="font-size: 1.3em; margin-bottom: 20px; color: #4a90e2;">{t("cpc_chart")}</h3>
                        {cpc_html}
                    </div>
                </div>
            </div>
        </div>
    </body>
    </html>
    """

    return html_content
