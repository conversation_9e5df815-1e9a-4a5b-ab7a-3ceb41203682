"""
Campaign Processor

Handles campaign data processing and business logic.
"""
import logging
import sys
import os

# Add the project root to the path so we can import modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

from items_report import generate_complete_perfromance_report
from campaign_reporting.data.campaign_data_service import (
    fetch_advertiser_campaigns,
    fetch_campaign_details,
    fetch_campaign_products
)
from campaign_reporting.data.file_service import (
    write_whatsapp_report,
    get_merchant_performance_report_path,
    append_campaign_to_csv,
    write_html_performance_report
)
from campaign_reporting.business.report_generator import generate_whatsapp_performance_report
from campaign_reporting.business.recommendation_service import process_merchant_recommendations

logger = logging.getLogger(__name__)


async def process_merchant_campaigns(merchant_info, locale="pt", session=None, generate_html=False):
    """
    Process all campaigns for a single merchant and generate recommendations.

    Args:
        merchant_info: Dictionary containing merchant and access_token
        locale: Language/locale for reports (default: pt)
        session: HTTP session for API calls (optional, for recommendations)
        generate_html: Whether to generate HTML files in addition to PDFs (default: False)

    Returns:
        List of processed campaigns
    """
    merchant = merchant_info['merchant']
    access_token = merchant_info['access_token']

    # Extract user_id from access_token for MercadoLibreClient
    user_id = access_token.split('-')[-1]
    user_id = int(user_id)

    logger.info(f"Processing campaigns for merchant {merchant}")

    # Fetch campaigns
    campaigns = await fetch_advertiser_campaigns(access_token, user_id)
    if not campaigns:
        return []

    campaigns_list = []
    all_campaign_products = []  # Collect campaign products for recommendations

    for campaign in campaigns[:1]:
        try:
            # Process individual campaign and collect products
            campaign_result, campaign_products = await process_single_campaign(
                campaign,
                access_token,
                user_id,
                merchant,
                locale,
                generate_html
            )

            if campaign_result:
                campaigns_list.append(campaign_result)

                # Write campaign data to CSV immediately after successful processing
                try:
                    append_campaign_to_csv(campaign_result, merchant)
                    logger.info(f"Campaign {campaign_result.get('id', 'unknown')} data written to CSV for merchant {merchant}")
                except Exception as e:
                    logger.error(f"Error writing campaign {campaign_result.get('id', 'unknown')} data to CSV for merchant {merchant}: {e}")

            # Collect campaign products for recommendations (even if campaign processing failed)
            if campaign_products:
                all_campaign_products.extend(campaign_products)

        except Exception as e:
            logger.error(f"Error processing campaign {campaign.get('id', 'unknown')}: {e}")
            continue

    logger.info(f"Processed {len(campaigns_list)} campaigns for merchant {merchant}")

    # Process recommendations if session is provided
    if session is not None:
        try:
            logger.info(f"Processing recommendations for merchant {merchant}")
            recommendation_success = await process_merchant_recommendations(
                session,
                merchant_info,
                locale,
                all_campaign_products  # Pass pre-fetched campaign products
            )
            if recommendation_success:
                logger.info(f"Successfully generated recommendations for merchant {merchant}")
            else:
                logger.warning(f"Failed to generate recommendations for merchant {merchant}")
        except Exception as e:
            logger.error(f"Error processing recommendations for merchant {merchant}: {e}")

    return campaigns_list




async def process_single_campaign(campaign, access_token, user_id, merchant, locale="pt", generate_html=False):
    """
    Process a single campaign.

    Args:
        campaign: Campaign data
        access_token: MercadoLibre API access token
        user_id: User ID
        merchant: Merchant identifier
        locale: Language/locale for reports (default: pt)
        generate_html: Whether to generate HTML files in addition to PDFs (default: False)

    Returns:
        Tuple of (processed campaign data or None, campaign products list)
    """
    campaign_id = campaign['id']
    date_created = campaign['date_created']

    logger.info(f"Processing campaign {campaign_id} for merchant {merchant}")

    # Fetch campaign details
    campaign_data = await fetch_campaign_details(access_token, user_id, campaign_id, date_created)
    if not campaign_data:
        return None, []

    # Only process campaigns with clicks
    if not campaign_data.get('metrics', {}).get("clicks"):
        logger.info(f"Campaign {campaign_id} has no clicks, skipping")
        return None, []

    try:
        # Convert locale to language code for WhatsApp report
        language = 'pt' if locale == 'pt' else 'es' if locale == 'es' else 'pt'

        # Generate WhatsApp report
        whatsapp_report = generate_whatsapp_performance_report(campaign_data,language)
        write_whatsapp_report(whatsapp_report, merchant, campaign_id)

        # Fetch campaign products for complete report
        campaign_products = []
        advertiser_id = await _get_advertiser_id(access_token, user_id)
        if advertiser_id:
            campaign_products = await fetch_campaign_products(
                access_token,
                user_id,
                advertiser_id,
                campaign_id
            )

            # Generate complete performance report with merchant-specific output
            await _generate_merchant_performance_report(
                campaign_data,
                campaign_products,
                campaign_id,
                merchant,
                access_token,
                locale,
                generate_html
            )

        # CSV writing is now handled at the merchant level after all campaigns are processed

        logger.info(f"Successfully processed campaign {campaign_id}")
        return campaign_data, campaign_products

    except Exception as e:
        logger.error(f"Error processing campaign {campaign_id}: {e}")
        return None, []


async def _get_advertiser_id(access_token, user_id):
    """
    Get advertiser ID for a user.

    Args:
        access_token: MercadoLibre API access token
        user_id: User ID

    Returns:
        Advertiser ID or None
    """
    from services.mercadolibre_service import MercadoLibreClient

    try:
        async with MercadoLibreClient(access_token, user_id) as client:
            return await client.get_advertiser_id_pads()
    except Exception as e:
        logger.error(f"Error getting advertiser ID: {e}")
        return None


def should_process_campaign(campaign_data):
    """
    Determine if a campaign should be processed.

    Args:
        campaign_data: Campaign data dictionary

    Returns:
        Boolean indicating if campaign should be processed
    """
    # Check if campaign has metrics and clicks
    metrics = campaign_data.get('metrics', {})
    return bool(metrics.get('clicks', 0) > 0)


def validate_campaign_data(campaign_data):
    """
    Validate campaign data structure.

    Args:
        campaign_data: Campaign data dictionary

    Returns:
        Boolean indicating if data is valid
    """
    required_fields = ['id', 'name', 'metrics', 'acos_target', 'budget']

    for field in required_fields:
        if field not in campaign_data:
            logger.warning(f"Missing required field: {field}")
            return False

    # Check metrics structure
    metrics = campaign_data.get('metrics', {})
    required_metrics = ['cost', 'total_amount', 'acos']

    for metric in required_metrics:
        if metric not in metrics:
            logger.warning(f"Missing required metric: {metric}")
            return False

    return True


async def _generate_merchant_performance_report(campaign_data, campaign_products, campaign_id, merchant, access_token, locale, generate_html=False):
    """
    Generate complete performance report and save to merchant-specific directory.

    Args:
        campaign_data: Campaign data
        campaign_products: Campaign products
        campaign_id: Campaign ID
        merchant: Merchant identifier
        access_token: API access token
        locale: Locale for report
        generate_html: Whether to generate HTML files in addition to PDFs (default: False)
    """

    from pdf_helper import convert_html_to_one_page_pdf


    try:
        # Generate the report using the original function
        # This returns HTML content as a string
        html_content = await generate_complete_perfromance_report(
            campaign_data,
            campaign_products,
            campaign_id,
            merchant,
            access_token,
            locale
        )

        if html_content:
            # Save HTML file if requested
            if generate_html:
                write_html_performance_report(html_content, merchant, campaign_id)

            # Create PDF filename
            pdf_filename = f"campaign_report_{campaign_id}.pdf"

            # Get merchant-specific path for the PDF in performance reports folder
            merchant_pdf_path = get_merchant_performance_report_path(merchant, pdf_filename)

            # Convert HTML string directly to PDF in the merchant's directory
            success = await convert_html_to_pdf(html_content, merchant_pdf_path)

            if success:
                logger.info(f"Generated PDF report at {merchant_pdf_path}")
            else:
                logger.error(f"Failed to generate PDF for campaign {campaign_id}")
        else:
            logger.warning(f"No HTML content generated for campaign {campaign_id}")

    except Exception as e:
        logger.error(f"Error generating merchant performance report: {e}")

