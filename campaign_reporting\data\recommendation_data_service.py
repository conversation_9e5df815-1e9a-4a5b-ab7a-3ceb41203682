"""
Recommendation Data Service

Handles all recommendation-related data access operations.
"""
import logging
import sys
import os
from datetime import datetime

# Add the project root to the path so we can import services
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

from services.mercadolibre_service import MercadoLibreClient
from campaign_reporting.config.settings import DATE_FROM, DATE_TO
from campaign_reporting.data.file_service import get_merchant_recommendation_report_path
from pdf_helper import convert_html_to_one_page_pdf

from jinja2 import Environment, FileSystemLoader

logger = logging.getLogger(__name__)


async def fetch_merchant_campaigns_and_products(access_token: str, user_id: int, merchant: str):
    """
    Fetch all campaigns and their products for a merchant.

    Args:
        access_token: MercadoLibre API access token
        user_id: User ID for API calls
        merchant: Merchant identifier for logging

    Returns:
        List of campaign products or None if error
    """
    try:
        async with MercadoLibreClient(access_token, user_id) as client:
            logger.info(f"Fetching advertiser ID for merchant {merchant}")
            advertiser_id = await client.get_advertiser_id_pads()

            if not advertiser_id:
                logger.error(f"Could not get advertiser ID for merchant {merchant}")
                return None

            # List campaigns
            logger.info(f"Listing campaigns for merchant {merchant} (advertiser {advertiser_id})")
            campaigns = await client.list_advertiser_campaigns(advertiser_id, DATE_FROM, DATE_TO)

            if not campaigns:
                logger.warning(f"No campaigns found for merchant {merchant}")
                return []

            campaign_products = []

            # Fetch products for each campaign with clicks
            for campaign in campaigns:
                logger.info(f"Fetching data for campaign {campaign['id']} (merchant {merchant})")

                # Adjust date if needed
                campaign_date = campaign['date_created']
                if campaign_date.split("-")[0] == '2025':
                    campaign_date = DATE_FROM

                # Fetch campaign data to check if it has clicks
                campaign_data = await client.fetch_campaign_data(
                    campaign['id'],
                    campaign_date.split("T")[0],
                    DATE_TO
                )

                # Only process campaigns with clicks
                if (campaign_data and
                    campaign_data.get('metrics') and
                    campaign_data.get('metrics', {}).get("clicks")):

                    logger.info(f"Listing product ads items for campaign {campaign['id']} (merchant {merchant})")
                    this_campaign_products = await client.list_product_ads_items(
                        advertiser_id,
                        campaign['id'],
                        campaign_date.split("T")[0],
                        DATE_TO
                    )
                    campaign_products.extend(this_campaign_products)

            logger.info(f"Found {len(campaign_products)} campaign products for merchant {merchant}")
            return campaign_products

    except Exception as e:
        logger.error(f"Error fetching campaign data for merchant {merchant}: {e}")
        return None


async def create_recommendation_report(df_unlisted, merchant: str, locale: str = "pt_BR"):
    """
    Create and save recommendation report for a merchant.

    Args:
        df_unlisted: DataFrame with unlisted recommended products
        merchant: Merchant identifier
        locale: Language/locale for the report
    """
    try:
        # Check if we have any data
        if df_unlisted.empty:
            logger.warning(f"No data to generate recommendation report for merchant {merchant}")
            return

        # Get store information
        store_name = df_unlisted['store_name'].iloc[0]
        store_permalink = df_unlisted['store_permalink'].iloc[0]


        # Format numeric columns for display (make a copy to avoid warnings)
        df_formatted = df_unlisted.copy()
        if 'price' in df_formatted.columns:
            df_formatted['price'] = df_formatted['price'].astype(float)
        if 'sales_potential' in df_formatted.columns:
            df_formatted['sales_potential'] = df_formatted['sales_potential'].astype(float)
        if 'conversion' in df_formatted.columns:
            df_formatted['conversion'] = df_formatted['conversion'].astype(float)

        # Set up Jinja2 environment - use existing template structure
        env = Environment(loader=FileSystemLoader('.'))


        template_file = 'produtos_recomendados.html'
        if locale == "es_AR":
            template_file = 'productos_recomendados.html'
 
        template = env.get_template(template_file)

        # Process data for template (split into recommended and others)
        df_rec = df_formatted[df_formatted.get('product_group', 2) == 2].copy()
        df_others = df_formatted[df_formatted.get('product_group', 1) == 1].copy()

        # If no product_group column exists, treat all as recommended
        if df_rec.empty and not df_formatted.empty:
            df_rec = df_formatted.copy()

        # Render HTML
        html_output = template.render(
            df_rec=df_rec,
            df_others=df_others,
            store_name=store_name,
            store_permalink=store_permalink,
            page_title_text='Recomendação de Produtos' if locale.startswith('pt') else 'Recomendación de Productos',
            locale=locale
        )

        # Generate filename
        sanitized_store_name = store_name.replace(" ", "_").replace("/", "_")
        date_str = datetime.now().strftime("%Y%m%d")
        pdf_filename = f'produtos_recomendados_{sanitized_store_name}_{merchant}_{date_str}.pdf'

        # Get merchant-specific path for the PDF in recommendations folder
        pdf_path = get_merchant_recommendation_report_path(merchant, pdf_filename)

        # Convert to PDF
        success = await convert_html_to_pdf(html_output, pdf_path)

        if success:
            logger.info(f"Generated recommendation report for merchant {merchant} at {pdf_path}")
        else:
            logger.error(f"Failed to generate PDF for merchant {merchant}")

    except Exception as e:
        logger.error(f"Error creating recommendation report for merchant {merchant}: {e}")


def _format_currency_value(value, locale: str = "pt_BR"):
    """
    Format currency value based on locale.

    Args:
        value: Numeric value to format
        locale: Locale for formatting

    Returns:
        Formatted currency string
    """
    if locale.startswith('pt'):
        return f"R${value:,.2f}"
    elif locale.startswith('es'):
        return f"ARS{value:,.2f}"
    else:
        return f"R${value:,.2f}"  # Default to Brazilian Real
